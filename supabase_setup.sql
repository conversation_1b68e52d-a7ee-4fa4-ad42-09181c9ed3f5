-- Binance Token Scraper - Supabase Database Setup
-- This file contains all the SQL commands needed to set up the database for the Binance Token Scraper application
-- Run this in the Supabase SQL Editor to create all necessary tables and security policies

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tokens table
CREATE TABLE IF NOT EXISTS tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ticker TEXT NOT NULL UNIQUE,
  source TEXT NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL,
  verified BOOLEAN NOT NULL DEFAULT FALSE,
  false_positive BOOLEAN NOT NULL DEFAULT FALSE,
  source_url TEXT,
  description TEXT,
  market_cap NUMERIC,
  price NUMERIC,
  price_change_24h NUMERIC,
  last_checked TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create telegram_messages table
CREATE TABLE IF NOT EXISTS telegram_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id BIGINT NOT NULL,
  text TEXT NOT NULL,
  date TIMESTAMPTZ NOT NULL,
  url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create twitter_tweets table
CREATE TABLE IF NOT EXISTS twitter_tweets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tweet_id TEXT NOT NULL,
  text TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  url TEXT NOT NULL
);

-- Create binance_announcements table
CREATE TABLE IF NOT EXISTS binance_announcements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  announcement_id TEXT NOT NULL,
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  date TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create scrape_results table
CREATE TABLE IF NOT EXISTS scrape_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  fetched_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tokens_ticker ON tokens(ticker);
CREATE INDEX IF NOT EXISTS idx_tokens_timestamp ON tokens(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_tokens_false_positive ON tokens(false_positive);
CREATE INDEX IF NOT EXISTS idx_telegram_messages_date ON telegram_messages(date DESC);
CREATE INDEX IF NOT EXISTS idx_twitter_tweets_created_at ON twitter_tweets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_binance_announcements_date ON binance_announcements(date DESC);
CREATE INDEX IF NOT EXISTS idx_scrape_results_fetched_at ON scrape_results(fetched_at DESC);

-- Create unique constraints to prevent duplicates
ALTER TABLE telegram_messages ADD CONSTRAINT unique_telegram_message_id UNIQUE (message_id);
ALTER TABLE twitter_tweets ADD CONSTRAINT unique_twitter_tweet_id UNIQUE (tweet_id);
ALTER TABLE binance_announcements ADD CONSTRAINT unique_binance_announcement_id UNIQUE (announcement_id);

-- Enable Row Level Security (RLS)
ALTER TABLE tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE twitter_tweets ENABLE ROW LEVEL SECURITY;
ALTER TABLE binance_announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE scrape_results ENABLE ROW LEVEL SECURITY;

-- The service_role is already created by Supabase automatically
-- This is just a safeguard in case it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'service_role') THEN
    CREATE ROLE service_role NOLOGIN;
  END IF;
END
$$;

-- Create policies for the service role
-- These policies allow the service role to perform all operations on the tables

-- Tokens table policies
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can select tokens') THEN
    CREATE POLICY "Service role can select tokens" ON tokens
      FOR SELECT TO service_role USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can insert tokens') THEN
    CREATE POLICY "Service role can insert tokens" ON tokens
      FOR INSERT TO service_role WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can update tokens') THEN
    CREATE POLICY "Service role can update tokens" ON tokens
      FOR UPDATE TO service_role USING (true) WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can delete tokens') THEN
    CREATE POLICY "Service role can delete tokens" ON tokens
      FOR DELETE TO service_role USING (true);
  END IF;
END $$;

-- Telegram messages table policies
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can select telegram_messages') THEN
    CREATE POLICY "Service role can select telegram_messages" ON telegram_messages
      FOR SELECT TO service_role USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can insert telegram_messages') THEN
    CREATE POLICY "Service role can insert telegram_messages" ON telegram_messages
      FOR INSERT TO service_role WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can update telegram_messages') THEN
    CREATE POLICY "Service role can update telegram_messages" ON telegram_messages
      FOR UPDATE TO service_role USING (true) WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can delete telegram_messages') THEN
    CREATE POLICY "Service role can delete telegram_messages" ON telegram_messages
      FOR DELETE TO service_role USING (true);
  END IF;
END $$;

-- Twitter tweets table policies
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can select twitter_tweets') THEN
    CREATE POLICY "Service role can select twitter_tweets" ON twitter_tweets
      FOR SELECT TO service_role USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can insert twitter_tweets') THEN
    CREATE POLICY "Service role can insert twitter_tweets" ON twitter_tweets
      FOR INSERT TO service_role WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can update twitter_tweets') THEN
    CREATE POLICY "Service role can update twitter_tweets" ON twitter_tweets
      FOR UPDATE TO service_role USING (true) WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can delete twitter_tweets') THEN
    CREATE POLICY "Service role can delete twitter_tweets" ON twitter_tweets
      FOR DELETE TO service_role USING (true);
  END IF;
END $$;

-- Binance announcements table policies
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can select binance_announcements') THEN
    CREATE POLICY "Service role can select binance_announcements" ON binance_announcements
      FOR SELECT TO service_role USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can insert binance_announcements') THEN
    CREATE POLICY "Service role can insert binance_announcements" ON binance_announcements
      FOR INSERT TO service_role WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can update binance_announcements') THEN
    CREATE POLICY "Service role can update binance_announcements" ON binance_announcements
      FOR UPDATE TO service_role USING (true) WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can delete binance_announcements') THEN
    CREATE POLICY "Service role can delete binance_announcements" ON binance_announcements
      FOR DELETE TO service_role USING (true);
  END IF;
END $$;

-- Scrape results table policies
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can select scrape_results') THEN
    CREATE POLICY "Service role can select scrape_results" ON scrape_results
      FOR SELECT TO service_role USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can insert scrape_results') THEN
    CREATE POLICY "Service role can insert scrape_results" ON scrape_results
      FOR INSERT TO service_role WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can update scrape_results') THEN
    CREATE POLICY "Service role can update scrape_results" ON scrape_results
      FOR UPDATE TO service_role USING (true) WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Service role can delete scrape_results') THEN
    CREATE POLICY "Service role can delete scrape_results" ON scrape_results
      FOR DELETE TO service_role USING (true);
  END IF;
END $$;

-- Create policies for anonymous users (public access)
-- These policies allow anonymous users to only read data

-- Tokens table policies for anonymous users
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anonymous users can select tokens') THEN
    CREATE POLICY "Anonymous users can select tokens" ON tokens
      FOR SELECT TO anon USING (true);
  END IF;
END $$;

-- Telegram messages table policies for anonymous users
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anonymous users can select telegram_messages') THEN
    CREATE POLICY "Anonymous users can select telegram_messages" ON telegram_messages
      FOR SELECT TO anon USING (true);
  END IF;
END $$;

-- Twitter tweets table policies for anonymous users
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anonymous users can select twitter_tweets') THEN
    CREATE POLICY "Anonymous users can select twitter_tweets" ON twitter_tweets
      FOR SELECT TO anon USING (true);
  END IF;
END $$;

-- Binance announcements table policies for anonymous users
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anonymous users can select binance_announcements') THEN
    CREATE POLICY "Anonymous users can select binance_announcements" ON binance_announcements
      FOR SELECT TO anon USING (true);
  END IF;
END $$;

-- Scrape results table policies for anonymous users
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anonymous users can select scrape_results') THEN
    CREATE POLICY "Anonymous users can select scrape_results" ON scrape_results
      FOR SELECT TO anon USING (true);
  END IF;
END $$;

-- Grant privileges to the service role
GRANT ALL ON TABLE tokens TO service_role;
GRANT ALL ON TABLE telegram_messages TO service_role;
GRANT ALL ON TABLE twitter_tweets TO service_role;
GRANT ALL ON TABLE binance_announcements TO service_role;
GRANT ALL ON TABLE scrape_results TO service_role;

-- Grant SELECT privileges to the anonymous role
GRANT SELECT ON TABLE tokens TO anon;
GRANT SELECT ON TABLE telegram_messages TO anon;
GRANT SELECT ON TABLE twitter_tweets TO anon;
GRANT SELECT ON TABLE binance_announcements TO anon;
GRANT SELECT ON TABLE scrape_results TO anon;
