{"name": "binance-bot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "NEXT_TELEMETRY_DISABLED=1 next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mendable/firecrawl-js": "^1.21.1", "@openrouter/ai-sdk-provider": "^0.4.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/supabase-js": "^2.49.8", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.5", "input": "^1.0.1", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "telegram": "^2.26.22", "twitter-api-v2": "^1.23.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "tw-animate-css": "^1.3.0", "typescript": "^5"}}