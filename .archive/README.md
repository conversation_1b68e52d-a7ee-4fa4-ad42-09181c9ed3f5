# Archive Directory

This directory contains files that were part of the development process but are no longer needed in the main codebase.

## dev-tools/

Contains development and debugging utilities that were used during development but are not part of the production application.

### Files:

- **check-exports.js** - Debugging script used to examine @mendable/firecrawl-js library exports
  - Created during Firecrawl integration troubleshooting
  - No longer needed as integration is working
  - Kept for potential future debugging reference

## Removed Files (No Archive)

The following files were completely removed as they were redundant:

- **next.config.ts** - Empty TypeScript config template (redundant with next.config.js)
- **package-lock.json** - npm lock file (conflicts with <PERSON><PERSON>'s bun.lockb)

## Archive Date

Files archived: 2025-06-19

## Project Status

The main codebase is clean and production-ready. The major cleanup was completed during the serverless migration to Supabase, which removed all legacy local file storage.