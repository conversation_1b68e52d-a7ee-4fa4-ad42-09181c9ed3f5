# Supabase Setup Guide for Binance Token Scraper

This guide explains how to set up your Supabase database for the Binance Token Scraper application.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com) if you don't have one)
- A Supabase project created

## Setup Steps

### 1. Create the Database Tables and Policies

1. Log in to your Supabase dashboard and select your project
2. Go to the SQL Editor (left sidebar)
3. Create a new query
4. Copy and paste the entire contents of the `supabase_setup.sql` file into the SQL Editor
5. Click "Run" to execute the SQL commands

This will create:
- All required tables (tokens, telegram_messages, twitter_tweets, binance_announcements, scrape_results)
- Appropriate indexes for better performance
- Row Level Security (RLS) policies for secure access
- Necessary roles and permissions

### 2. Get Your Supabase Credentials

After setting up the database, you need to get your Supabase URL and service role key:

1. In your Supabase dashboard, go to Project Settings > API
2. Copy the "Project URL" - this is your `NEXT_PUBLIC_SUPABASE_URL`
3. Under "Project API keys", copy the "service_role" key - this is your `SUPABASE_SERVICE_ROLE_KEY`

### 3. Update Your Environment Variables

Add these credentials to your `.env` file:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. Migrate Existing Data (Optional)

If you have existing data in local JSON files, you can migrate it to Supabase:

```bash
npx tsx scripts/migrate-to-supabase.ts
```

## Database Schema

### tokens
- `id`: UUID (Primary Key)
- `ticker`: TEXT (Unique, Not Null) - The token ticker symbol
- `source`: TEXT (Not Null) - Where the token was discovered (Telegram, Twitter, Binance)
- `timestamp`: TIMESTAMPTZ (Not Null) - When the token was discovered
- `verified`: BOOLEAN (Default: false) - Whether the token has been verified
- `false_positive`: BOOLEAN (Default: false) - Whether the token is a false positive
- `source_url`: TEXT - URL to the original source
- `description`: TEXT - Token description
- `market_cap`: NUMERIC - Market capitalization
- `price`: NUMERIC - Current price
- `price_change_24h`: NUMERIC - 24-hour price change percentage
- `last_checked`: TIMESTAMPTZ - When the token data was last updated
- `created_at`: TIMESTAMPTZ (Default: NOW()) - When the record was created

### telegram_messages
- `id`: UUID (Primary Key)
- `message_id`: BIGINT (Not Null, Unique) - Telegram message ID
- `text`: TEXT (Not Null) - Message content
- `date`: TIMESTAMPTZ (Not Null) - When the message was posted
- `url`: TEXT - URL to the message
- `created_at`: TIMESTAMPTZ (Default: NOW()) - When the record was created

### twitter_tweets
- `id`: UUID (Primary Key)
- `tweet_id`: TEXT (Not Null, Unique) - Twitter tweet ID
- `text`: TEXT (Not Null) - Tweet content
- `created_at`: TIMESTAMPTZ (Not Null) - When the tweet was posted
- `url`: TEXT (Not Null) - URL to the tweet

### binance_announcements
- `id`: UUID (Primary Key)
- `announcement_id`: TEXT (Not Null, Unique) - Binance announcement ID
- `title`: TEXT (Not Null) - Announcement title
- `url`: TEXT (Not Null) - URL to the announcement
- `date`: TIMESTAMPTZ (Not Null) - When the announcement was posted
- `created_at`: TIMESTAMPTZ (Default: NOW()) - When the record was created

### scrape_results
- `id`: UUID (Primary Key)
- `fetched_at`: TIMESTAMPTZ (Not Null) - When the scrape was performed
- `created_at`: TIMESTAMPTZ (Default: NOW()) - When the record was created

## Security

The database is configured with Row Level Security (RLS) policies:

- The service role has full access to all tables (SELECT, INSERT, UPDATE, DELETE)
- Anonymous users (public access) can only read data (SELECT)

This ensures that your data is secure while still allowing the application to function properly.

## Troubleshooting

### Common Issues

1. **Duplicate Key Violations**: If you're seeing errors about duplicate keys when migrating data, it means you're trying to insert records that already exist. You can modify the migration script to handle this case.

2. **Permission Denied Errors**: Make sure you're using the service role key for operations that modify data. The anon key only has read permissions.

3. **Missing Tables**: If you see errors about missing tables, make sure you've run the entire SQL script successfully.

For any other issues, check the Supabase logs in your project dashboard under "Database" > "Logs".
