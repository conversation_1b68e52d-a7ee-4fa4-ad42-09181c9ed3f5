// Simple script to check the exports of the firecrawl library
const firecrawl = require('@mendable/firecrawl-js');

console.log('Available exports in @mendable/firecrawl-js:');
console.log(Object.keys(firecrawl));

// Check if there are any nested exports
for (const key of Object.keys(firecrawl)) {
  if (typeof firecrawl[key] === 'object') {
    console.log(`\nNested exports in ${key}:`);
    console.log(Object.keys(firecrawl[key]));
  }
}
