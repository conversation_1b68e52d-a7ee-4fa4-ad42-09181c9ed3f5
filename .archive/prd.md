Below is an **updated PRD** that folds in automated token-discovery with the **Vercel AI SDK**, using the **OpenRouter** provider and the **google / gemini-2.5-flash-preview-05-20** model. It keeps the rest of the stack (GramJS, twitter-api-v2, Firecrawl) and still limits every scrape to **five items per source**.

---

## Executive summary

Every hour a Next.js API route (triggered by a Vercel cron job)

1. pulls the five newest messages from Binance’s official Telegram channel, BinanceWallet’s Twitter account, and Binance’s “New Cryptocurrency Listing” page;
2. asks <PERSON><PERSON><PERSON> (via OpenRouter + Vercel AI SDK) to detect any token tickers that do **not** exist in our local database;
3. appends just those new tickers—along with source and timestamp—into an **`addedTokens`** field in `data/crypto_announcements.json`. This end-to-end flow relies only on maintained, first-party libraries and stays within free-plan rate limits for every service.

---

## 1 Key libraries & maintenance status

| Purpose          | Library                                   | Latest publish                                                                         | Notes                                                  |
| ---------------- | ----------------------------------------- | -------------------------------------------------------------------------------------- | ------------------------------------------------------ |
| Telegram MTProto | **GramJS** (`telegram`)                   | 2.26.22 – 3 mo ago ([npm][1])                                                          | Official MTProto client; 1.5 k⭐ repo, active commits.  |
| Twitter v2       | **twitter-api-v2**                        | 1.23.2 – 10 days ago ([npm][2])                                                        | Full-featured, strongly-typed; 150 k weekly downloads. |
| Web scraping     | **@mendable/firecrawl-js**                | Continuous releases; rate-limit 10 req/min on free plan ([Firecrawl Documentation][3]) | Resilient markdown/HTML output.                        |
| AI runtime       | **Vercel AI SDK**                         | v0.23 (2 mo ago) ([Vercel][4])                                                         | Unified streaming API across providers.                |
| AI provider      | **@openrouter/ai-sdk-provider**           | v0.4.6 – May 17 2025 ([GitHub][5])                                                     | Bridges AI SDK to OpenRouter’s 300+ models.            |
| Model            | **google/gemini-2.5-flash-preview-05-20** | Added Apr 17 2025 on OpenRouter ([OpenRouter][6])                                      |                                                        |

All projects show recent releases and active repos, satisfying long-term robustness goals.

---

## 2 Updated architecture

```
Vercel Cron (0 * * * *) ─► /api/scrape
                              │
                              ├─ GramJS  → Telegram (5 msgs)
                              ├─ twitter-api-v2 → Twitter (5 tweets)
                              ├─ Firecrawl → Binance page (5 cards)
                              │
                              ├─ Vercel AI SDK + OpenRouter
                              │      └─ gemini-2.5-flash-preview analyses combined text
                              │
                              ├─ token-dedupe against local DB (tokens.json)
                              └─ write data/crypto_announcements.json
```

A single serverless function orchestrates everything, keeping cold-start latency low and avoiding extra infra.

---

## 3 Data flow & token discovery logic

### 3.1 Scraping (unchanged)

* **Telegram:** `messages.getHistory(limit: 5)` via GramJS ([npm][1])
* **Twitter:** `v2.userTimeline(max_results: 5)` via twitter-api-v2 ([npm][7])
* **Binance:** scrape list 48, slice first 5 announcement links with Cheerio after Firecrawl fetch ([Binance][8])

### 3.2 AI-powered token extraction

1. Build a **prompt** listing already-known tickers from `tokens.json` plus the new scrape body.
2. Call `generateText()` (or `streamText()`) on the AI SDK:

```ts
import { generateText } from 'ai';
import OpenRouter from '@openrouter/ai-sdk-provider';

const ai = new OpenRouter({ apiKey: process.env.OPENROUTER_API_KEY });
const resp = await generateText({
  model: 'google/gemini-2.5-flash-preview-05-20',
  provider: ai,
  messages: [
    { role: 'system', content:
      'Return, as JSON array, any NEW crypto tickers (3-6 all-caps chars) you find that are NOT in the "known" list.' },
    { role: 'user', content: JSON.stringify({ known: knownTokens, text: scrapeText }) }
  ]
});
const { tokens }: { tokens: { symbol: string, source: string }[] } = JSON.parse(await resp.text());
```

OpenRouter uses standard `Bearer <key>` auth header ([OpenRouter][9]), so no extra wiring beyond the provider install is required.

### 3.3 Deduplication & persistence

* Load `tokens.json` (our simple DB).
* For each AI-returned ticker not already present, push to both `tokens.json` and the **`addedTokens`** array of the hourly file with `{ ticker, source, ts }`.
* This guarantees a token is recorded exactly once, even if Binance re-announces it or multiple sources mention it.

---

## 4 JSON schema (hourly output)

```jsonc
{
  "telegram": [ /* ≤5 messages */ ],
  "twitter":  [ /* ≤5 tweets   */ ],
  "announcements": [ /* ≤5 links */ ],
  "addedTokens": [
    { "ticker": "HAEDAL", "source": "Binance Announcement", "timestamp": "2025-05-22T12:00:04Z" }
  ],
  "fetchedAt": "2025-05-22T12:00:00Z"
}
```

---

## 5 Code outline (delta-only)

### 5.1 `utils/analyzeTokens.ts`

```ts
import fs from 'fs/promises';
import OpenRouter from '@openrouter/ai-sdk-provider';
import { generateText } from 'ai';

const ai = new OpenRouter({ apiKey: process.env.OPENROUTER_API_KEY });

export async function findNewTickers(allText: string, source: string) {
  const dbPath = './data/tokens.json';
  const known: string[] = JSON.parse(await fs.readFile(dbPath, 'utf8'));

  const res = await generateText({
    model: 'google/gemini-2.5-flash-preview-05-20',
    provider: ai,
    messages: [
      { role: 'system', content: 'Return ONLY new ticker symbols as JSON array.' },
      { role: 'user', content: JSON.stringify({ known, text: allText }) }
    ],
  });

  const tokens: string[] = JSON.parse(await res.text());
  const timestamp = new Date().toISOString();

  if (tokens.length) {
    await fs.writeFile(dbPath, JSON.stringify([...known, ...tokens], null, 2));
  }

  return tokens.map(ticker => ({ ticker, source, timestamp }));
}
```

### 5.2 Updated `/api/scrape.ts` (excerpt)

```ts
const [telegram, twitter, announcements] = await Promise.all([
  getTelegramPosts(), getTwitterPosts(), getBinanceAnns()
]);

const scrapeText = [
  ...telegram.map(m => m.text),
  ...twitter.map(t => t.text),
  ...announcements.map(a => a.title)
].join('\n');

const addedTokens = await findNewTickers(scrapeText, 'auto');

const payload = { telegram, twitter, announcements, addedTokens, fetchedAt: new Date().toISOString() };
await fs.writeFile('./data/crypto_announcements.json', JSON.stringify(payload, null, 2));
```

---

## 6 Vercel cron & env vars (unchanged)

```jsonc
{
  "crons": [
    { "path": "/api/scrape", "schedule": "0 * * * *" }
  ]
}
```

Cron jobs are GA and configured via `vercel.json` ([Vercel][10]).
**Required secrets:** `TG_API_ID`, `TG_API_HASH`, `TG_SESSION_STRING`, `TW_BEARER`, `FIRECRAWL_API_KEY`, `OPENROUTER_API_KEY`.

---

## 7 Edge cases & mitigation

* **Binance HTML drift** – Firecrawl shields us from most selector breakage; we still slice only the first 5 cards, so even a new banner won’t crash parsing ([Binance][8]).
* **LLM hallucination** – Gemini’s JSON-only prompt plus a downstream regex check like `/^[A-Z0-9]{3,6}$/` ensures invalid outputs are ignored, aligning with best practice for info-extraction via LLMs ([Medium][11]).
* **API limits** – Five requests per hour stay well below Firecrawl’s free limit (10/min) ([Firecrawl Documentation][3]) and Telegram/Twitter read limits.
* **Model or provider downtime** – Wrap the AI call in `try/catch`; on failure we log and store `addedTokens: []`, ensuring the scrape still completes.

---

### Conclusion

With the AI layer now integrated, the system not only mirrors Binance listing posts but **automatically flags entirely new tickers**—giving you an hourly, trustworthy watch-list without manual regex juggling or extra services.

[1]: https://www.npmjs.com/package/telegram?utm_source=chatgpt.com "telegram - NPM"
[2]: https://www.npmjs.com/package/twitter-api-v2?utm_source=chatgpt.com "twitter-api-v2 - NPM"
[3]: https://docs.firecrawl.dev/rate-limits?utm_source=chatgpt.com "Rate Limits - Firecrawl"
[4]: https://vercel.com/docs/ai-sdk?utm_source=chatgpt.com "AI SDK - Vercel"
[5]: https://github.com/OpenRouterTeam/ai-sdk-provider?utm_source=chatgpt.com "OpenRouterTeam/ai-sdk-provider: The OpenRouter ... - GitHub"
[6]: https://openrouter.ai/google/gemini-2.5-flash-preview?utm_source=chatgpt.com "Gemini 2.5 Flash Preview 04-17 - API, Providers, Stats - OpenRouter"
[7]: https://www.npmjs.com/package/twitter-api-v2 "twitter-api-v2 - npm"
[8]: https://www.binance.com/en/support/announcement/list/48?utm_source=chatgpt.com "New Cryptocurrency Listing | Binance Support"
[9]: https://openrouter.ai/docs/api-reference/authentication?utm_source=chatgpt.com "API Authentication | OpenRouter OAuth and API Keys"
[10]: https://vercel.com/docs/cron-jobs "Cron Jobs"
[11]: https://medium.com/%40gurpartap.sandhu3/title-using-llms-to-extract-structured-data-from-text-f3578fdc365b?utm_source=chatgpt.com "{'Title': 'Using LLMs to extract structured data from text'} | by GP Sandhu"
