# Supabase Troubleshooting Guide

This guide provides solutions for common issues you might encounter when setting up and using Supabase with the Binance Token Scraper application.

## Connection Issues

### Error: "Failed to connect to Supabase"

**Possible causes:**
1. Incorrect Supabase URL or service role key
2. Network connectivity issues
3. Supabase service is down

**Solutions:**
1. Verify your credentials in the `.env` file:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```
2. Make sure you're using the service role key, not the anon key
3. Check if you can access your Supabase dashboard in a browser
4. Run the test script: `npx tsx scripts/test-supabase-connection.ts`

### Error: "Supabase client not initialized"

**Possible causes:**
1. Environment variables not loaded
2. Missing or incorrect Supabase credentials

**Solutions:**
1. Make sure your `.env` file is in the project root directory
2. Verify that the environment variables are being loaded correctly
3. Check for typos in the variable names

## Database Issues

### Error: "relation 'tokens' does not exist"

**Possible causes:**
1. Database tables haven't been created
2. SQL script execution failed

**Solutions:**
1. Run the SQL setup script in the Supabase SQL Editor
2. Check for any errors in the SQL execution
3. Verify that all tables were created successfully

### Error: "duplicate key value violates unique constraint"

**Possible causes:**
1. Trying to insert a record with a key that already exists
2. Migration script not handling duplicates

**Solutions:**
1. Modify your code to use upsert instead of insert:
   ```typescript
   const { error } = await supabase
     .from('tokens')
     .upsert({ ticker: 'BTC', source: 'test', timestamp: new Date().toISOString() })
     .select();
   ```
2. Add conflict handling to your migration script

### Error: "new row violates row-level security policy"

**Possible causes:**
1. Using the anon key for write operations
2. Missing RLS policies

**Solutions:**
1. Make sure you're using the service role key for write operations
2. Check that the RLS policies have been created correctly
3. Verify that the service role has the necessary permissions

## Migration Issues

### Error during migration script execution

**Possible causes:**
1. Data format incompatibility
2. Missing fields in the source data
3. Network or connection issues

**Solutions:**
1. Add error handling to your migration script:
   ```typescript
   try {
     // Migration code
   } catch (error) {
     console.error('Migration error:', error);
   }
   ```
2. Process data in smaller batches
3. Add logging to identify problematic records

### Data not appearing after migration

**Possible causes:**
1. Migration script didn't complete successfully
2. Data was inserted into the wrong tables
3. Query issues when retrieving data

**Solutions:**
1. Check the migration script logs for errors
2. Verify data in the Supabase Table Editor
3. Test queries directly in the SQL Editor

## Performance Issues

### Slow queries

**Possible causes:**
1. Missing indexes
2. Inefficient queries
3. Large data volume

**Solutions:**
1. Make sure all recommended indexes are created:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_tokens_ticker ON tokens(ticker);
   CREATE INDEX IF NOT EXISTS idx_tokens_timestamp ON tokens(timestamp DESC);
   ```
2. Optimize your queries (use filters, limit results)
3. Consider pagination for large result sets

## Security Issues

### Unauthorized access to data

**Possible causes:**
1. Incorrect RLS policies
2. Using service role key in client-side code

**Solutions:**
1. Review and update RLS policies
2. Never expose the service role key in client-side code
3. Use the anon key for client-side operations and implement proper RLS

## Testing Your Setup

To verify that your Supabase setup is working correctly, run the test script:

```bash
npx tsx scripts/test-supabase-connection.ts
```

This script will:
1. Test the connection to your Supabase database
2. Verify that all required tables exist and are accessible
3. Test write permissions by inserting and deleting a test record

## Common Error Messages and Solutions

### "TypeError: Cannot read properties of undefined (reading 'from')"

**Solution:** Your Supabase client wasn't initialized properly. Check your environment variables.

### "FetchError: request to https://your-project.supabase.co/rest/v1/tokens failed"

**Solution:** Network connectivity issue or incorrect Supabase URL. Verify your URL and internet connection.

### "Error: JWT token is invalid"

**Solution:** Your service role key is incorrect or expired. Get a new key from your Supabase dashboard.

### "Error: permission denied for table tokens"

**Solution:** RLS policies are preventing access. Make sure the correct policies are in place.

### "ERROR: 42710: role 'service_role' already exists"

**Solution:** This is not actually an error. Supabase automatically creates the `service_role` role for each project. You can safely ignore this message when running the setup script. The script has been updated to handle this case gracefully.

## Getting Help

If you're still experiencing issues after trying these solutions:

1. Check the Supabase documentation: [https://supabase.com/docs](https://supabase.com/docs)
2. Look for similar issues in the Supabase GitHub repository: [https://github.com/supabase/supabase/issues](https://github.com/supabase/supabase/issues)
3. Ask for help in the Supabase Discord community: [https://discord.supabase.com](https://discord.supabase.com)
4. Review your application logs for more detailed error information
