/**
 * Utility functions for standardized error handling and responses
 */

import { NextResponse } from 'next/server';
import logger from '@/lib/logger';

/**
 * Error categories for classification
 */
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  CONFLICT = 'conflict',
  EXTERNAL_SERVICE = 'external_service',
  RATE_LIMIT = 'rate_limit',
  INTERNAL = 'internal',
  UNKNOWN = 'unknown',
}

/**
 * HTTP status codes mapped to error categories
 */
export const HTTP_STATUS_CODES = {
  [ErrorCategory.VALIDATION]: 400,
  [ErrorCategory.AUTHENTICATION]: 401,
  [ErrorCategory.AUTHORIZATION]: 403,
  [ErrorCategory.NOT_FOUND]: 404,
  [ErrorCategory.CONFLICT]: 409,
  [ErrorCategory.EXTERNAL_SERVICE]: 502,
  [ErrorCategory.RATE_LIMIT]: 429,
  [ErrorCategory.INTERNAL]: 500,
  [ErrorCategory.UNKNOWN]: 500,
};

/**
 * Standard error response structure
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    category: ErrorCategory;
    details?: Record<string, unknown>;
  };
  requestId?: string;
}

/**
 * Application error class with standardized structure
 */
export class AppError extends Error {
  category: ErrorCategory;
  code: string;
  details?: Record<string, unknown>;
  cause?: Error;

  constructor(
    message: string,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    code: string = 'error',
    details?: Record<string, unknown>,
    cause?: Error
  ) {
    super(message);
    this.name = 'AppError';
    this.category = category;
    this.code = code;
    this.details = details;
    this.cause = cause;
  }

  /**
   * Create a validation error
   */
  static validation(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.VALIDATION,
      'validation_error',
      details
    );
  }

  /**
   * Create an authentication error
   */
  static authentication(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.AUTHENTICATION,
      'authentication_error',
      details
    );
  }

  /**
   * Create an authorization error
   */
  static authorization(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.AUTHORIZATION,
      'authorization_error',
      details
    );
  }

  /**
   * Create a not found error
   */
  static notFound(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.NOT_FOUND,
      'not_found_error',
      details
    );
  }

  /**
   * Create a conflict error
   */
  static conflict(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.CONFLICT,
      'conflict_error',
      details
    );
  }

  /**
   * Create an external service error
   */
  static externalService(message: string, details?: Record<string, unknown>, cause?: Error): AppError {
    return new AppError(
      message,
      ErrorCategory.EXTERNAL_SERVICE,
      'external_service_error',
      details,
      cause
    );
  }

  /**
   * Create a rate limit error
   */
  static rateLimit(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(
      message,
      ErrorCategory.RATE_LIMIT,
      'rate_limit_error',
      details
    );
  }

  /**
   * Create an internal error
   */
  static internal(message: string, details?: Record<string, unknown>, cause?: Error): AppError {
    return new AppError(
      message,
      ErrorCategory.INTERNAL,
      'internal_error',
      details,
      cause
    );
  }

  /**
   * Convert any error to an AppError
   */
  static from(error: unknown, defaultMessage = 'An unexpected error occurred'): AppError {
    if (error instanceof AppError) {
      return error;
    }

    if (error instanceof Error) {
      return new AppError(
        error.message || defaultMessage,
        ErrorCategory.UNKNOWN,
        'unknown_error',
        undefined,
        error
      );
    }

    return new AppError(
      typeof error === 'string' ? error : defaultMessage,
      ErrorCategory.UNKNOWN,
      'unknown_error'
    );
  }

  /**
   * Get HTTP status code for this error
   */
  getStatusCode(): number {
    return HTTP_STATUS_CODES[this.category] || 500;
  }

  /**
   * Convert to a standard error response object
   */
  toResponse(requestId?: string): ErrorResponse {
    return {
      success: false,
      error: {
        message: this.message,
        code: this.code,
        category: this.category,
        details: this.details,
      },
      requestId,
    };
  }

  /**
   * Convert to a NextResponse for API routes
   */
  toNextResponse(requestId?: string): NextResponse {
    const responseBody = this.toResponse(requestId);
    
    // Log the error
    logger.error(`API Error: ${this.message}`, this, {
      category: this.category,
      code: this.code,
      statusCode: this.getStatusCode(),
      requestId,
      details: this.details
    });
    
    return NextResponse.json(
      responseBody,
      { status: this.getStatusCode() }
    );
  }
}

/**
 * Safely handle async operations with standardized error handling
 * @param fn Async function to execute
 * @param errorHandler Optional custom error handler
 * @returns Result of the async function or throws AppError
 */
export async function safeAsync<T>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => AppError
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (errorHandler) {
      throw errorHandler(error);
    }
    throw AppError.from(error);
  }
}

/**
 * Create a standardized API route handler with error handling
 * @param handler API route handler function
 * @returns NextResponse
 */
export function createApiHandler(
  handler: (req: Request) => Promise<NextResponse>
): (req: Request) => Promise<NextResponse> {
  return async (req: Request) => {
    try {
      return await handler(req);
    } catch (error) {
      const appError = AppError.from(error);
      return appError.toNextResponse();
    }
  };
}
