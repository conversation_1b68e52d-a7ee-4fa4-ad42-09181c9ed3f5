// Telegram message type
export interface TelegramMessage {
  id: number;
  text: string;
  date: Date;
  url?: string;
  channel?: string;
  link?: string;
}

// Twitter tweet type
export interface TwitterTweet {
  id: string;
  text: string;
  created_at?: string;
  createdAt?: string;
  url?: string;
  link?: string;
  username?: string;
}

// Binance announcement type
export interface BinanceAnnouncement {
  id: string;
  title: string;
  url?: string;
  link?: string;
  date: string;
}

// Token type
export interface Token {
  ticker: string;
  source: string;
  timestamp: string;
  verified?: boolean;
  falsePositive?: boolean;
  description?: string;
  marketCap?: number;
  price?: number;
  priceChange24h?: number;
  lastChecked?: string;
  sourceUrl?: string;  // URL to the original source where the token was found
  scrapeId?: string;   // ID of the scrape that found this token
}

// Scrape result type
export interface ScrapeResult {
  telegram: TelegramMessage[];
  twitter: TwitterTweet[];
  announcements: BinanceAnnouncement[];
  addedTokens: Token[];
  fetchedAt: string;
}

// AI response type
export interface AITokenResponse {
  tokens: string[];
}
