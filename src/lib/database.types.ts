export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      tokens: {
        Row: {
          id: string
          ticker: string
          source: string
          timestamp: string
          verified: boolean
          false_positive: boolean
          source_url: string | null
          description: string | null
          market_cap: number | null
          price: number | null
          price_change_24h: number | null
          last_checked: string | null
          created_at: string
        }
        Insert: {
          id?: string
          ticker: string
          source: string
          timestamp: string
          verified?: boolean
          false_positive?: boolean
          source_url?: string | null
          description?: string | null
          market_cap?: number | null
          price?: number | null
          price_change_24h?: number | null
          last_checked?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          ticker?: string
          source?: string
          timestamp?: string
          verified?: boolean
          false_positive?: boolean
          source_url?: string | null
          description?: string | null
          market_cap?: number | null
          price?: number | null
          price_change_24h?: number | null
          last_checked?: string | null
          created_at?: string
        }
        Relationships: []
      }
      telegram_messages: {
        Row: {
          id: string
          message_id: number
          text: string
          date: string
          url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          message_id: number
          text: string
          date: string
          url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          message_id?: number
          text?: string
          date?: string
          url?: string | null
          created_at?: string
        }
        Relationships: []
      }
      twitter_tweets: {
        Row: {
          id: string
          tweet_id: string
          text: string
          created_at: string
          url: string
        }
        Insert: {
          id?: string
          tweet_id: string
          text: string
          created_at: string
          url: string
        }
        Update: {
          id?: string
          tweet_id?: string
          text?: string
          created_at?: string
          url?: string
        }
        Relationships: []
      }
      binance_announcements: {
        Row: {
          id: string
          announcement_id: string
          title: string
          url: string
          date: string
          created_at: string
        }
        Insert: {
          id?: string
          announcement_id: string
          title: string
          url: string
          date: string
          created_at?: string
        }
        Update: {
          id?: string
          announcement_id?: string
          title?: string
          url?: string
          date?: string
          created_at?: string
        }
        Relationships: []
      }
      scrape_results: {
        Row: {
          id: string
          fetched_at: string
          created_at: string
        }
        Insert: {
          id?: string
          fetched_at: string
          created_at?: string
        }
        Update: {
          id?: string
          fetched_at?: string
          created_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
