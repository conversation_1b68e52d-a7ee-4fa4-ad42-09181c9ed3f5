/**
 * Health check system for monitoring service status and dependencies
 */

import logger from './logger';
import { isSupabaseConfigured } from './supabase';
import { getRateLimitStatus } from './rateLimitUtils';
import { getAllSessionStatuses } from './sessionManager';
import { getMemoryStats, formatBytes } from './memoryUtils';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  details?: Record<string, unknown>;
  lastChecked: string;
  responseTime?: number;
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: HealthCheckResult[];
  summary: {
    healthy: number;
    degraded: number;
    unhealthy: number;
    total: number;
  };
  timestamp: string;
}

/**
 * Check database connectivity
 */
async function checkDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    if (!isSupabaseConfigured()) {
      return {
        service: 'database',
        status: 'unhealthy',
        message: 'Supabase not configured',
        details: { error: 'Missing environment variables' },
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime
      };
    }

    // Try to import and test Supabase connection
    const { supabase } = await import('./supabase');
    const { data, error } = await supabase
      .from('tokens')
      .select('count')
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is OK
      return {
        service: 'database',
        status: 'unhealthy',
        message: 'Database connection failed',
        details: { error: error.message, code: error.code },
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime
      };
    }

    return {
      service: 'database',
      status: 'healthy',
      message: 'Database connection successful',
      lastChecked: new Date().toISOString(),
      responseTime: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'database',
      status: 'unhealthy',
      message: 'Database health check failed',
      details: { error: error instanceof Error ? error.message : String(error) },
      lastChecked: new Date().toISOString(),
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * Check external API configurations
 */
async function checkExternalAPIs(): Promise<HealthCheckResult[]> {
  const results: HealthCheckResult[] = [];
  
  // Check Twitter API
  const twitterStatus = process.env.TW_BEARER ? 'healthy' : 'degraded';
  results.push({
    service: 'twitter-api',
    status: twitterStatus,
    message: twitterStatus === 'healthy' ? 'Twitter API configured' : 'Twitter API not configured',
    details: { configured: !!process.env.TW_BEARER },
    lastChecked: new Date().toISOString()
  });

  // Check Telegram API
  const telegramConfigured = !!(process.env.TG_API_ID && process.env.TG_API_HASH && process.env.TG_SESSION_STRING);
  const telegramStatus = telegramConfigured ? 'healthy' : 'degraded';
  results.push({
    service: 'telegram-api',
    status: telegramStatus,
    message: telegramStatus === 'healthy' ? 'Telegram API configured' : 'Telegram API not configured',
    details: { configured: telegramConfigured },
    lastChecked: new Date().toISOString()
  });

  // Check Firecrawl API
  const firecrawlStatus = process.env.FIRECRAWL_API_KEY ? 'healthy' : 'degraded';
  results.push({
    service: 'firecrawl-api',
    status: firecrawlStatus,
    message: firecrawlStatus === 'healthy' ? 'Firecrawl API configured' : 'Firecrawl API not configured',
    details: { configured: !!process.env.FIRECRAWL_API_KEY },
    lastChecked: new Date().toISOString()
  });

  // Check OpenRouter API
  const openrouterStatus = process.env.OPENROUTER_API_KEY ? 'healthy' : 'degraded';
  results.push({
    service: 'openrouter-api',
    status: openrouterStatus,
    message: openrouterStatus === 'healthy' ? 'OpenRouter API configured' : 'OpenRouter API not configured',
    details: { configured: !!process.env.OPENROUTER_API_KEY },
    lastChecked: new Date().toISOString()
  });

  return results;
}

/**
 * Check memory usage
 */
function checkMemoryUsage(): HealthCheckResult {
  try {
    const stats = getMemoryStats();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    let message = 'Memory usage normal';

    if (stats.heapUsagePercentage > 90) {
      status = 'unhealthy';
      message = 'Critical memory usage';
    } else if (stats.heapUsagePercentage > 75) {
      status = 'degraded';
      message = 'High memory usage';
    }

    return {
      service: 'memory',
      status,
      message,
      details: {
        heapUsed: formatBytes(stats.heapUsed),
        heapTotal: formatBytes(stats.heapTotal),
        heapUsagePercentage: `${stats.heapUsagePercentage.toFixed(2)}%`,
        rss: formatBytes(stats.rss)
      },
      lastChecked: new Date().toISOString()
    };
  } catch (error) {
    return {
      service: 'memory',
      status: 'unhealthy',
      message: 'Memory check failed',
      details: { error: error instanceof Error ? error.message : String(error) },
      lastChecked: new Date().toISOString()
    };
  }
}

/**
 * Check rate limiting status
 */
function checkRateLimits(): HealthCheckResult {
  try {
    const services = ['twitter-api', 'telegram-api', 'firecrawl-api', 'openrouter-api'];
    const rateLimitDetails: Record<string, unknown> = {};
    let hasIssues = false;

    for (const service of services) {
      const status = getRateLimitStatus(service);
      if (status) {
        rateLimitDetails[service] = {
          tokens: status.tokens,
          queueLength: status.queueLength,
          lastRefill: new Date(status.lastRefill).toISOString()
        };

        // Check if there are queued requests (potential bottleneck)
        if (status.queueLength > 5) {
          hasIssues = true;
        }
      }
    }

    return {
      service: 'rate-limits',
      status: hasIssues ? 'degraded' : 'healthy',
      message: hasIssues ? 'Some services have queued requests' : 'Rate limits normal',
      details: rateLimitDetails,
      lastChecked: new Date().toISOString()
    };
  } catch (error) {
    return {
      service: 'rate-limits',
      status: 'unhealthy',
      message: 'Rate limit check failed',
      details: { error: error instanceof Error ? error.message : String(error) },
      lastChecked: new Date().toISOString()
    };
  }
}

/**
 * Check session status
 */
function checkSessions(): HealthCheckResult {
  try {
    const sessions = getAllSessionStatuses();
    const sessionDetails: Record<string, unknown> = {};
    let hasIssues = false;

    for (const [serviceName, status] of Object.entries(sessions)) {
      sessionDetails[serviceName] = status;
      
      if (status.exists && status.needsValidation) {
        hasIssues = true;
      }
    }

    return {
      service: 'sessions',
      status: hasIssues ? 'degraded' : 'healthy',
      message: hasIssues ? 'Some sessions need validation' : 'All sessions valid',
      details: sessionDetails,
      lastChecked: new Date().toISOString()
    };
  } catch (error) {
    return {
      service: 'sessions',
      status: 'unhealthy',
      message: 'Session check failed',
      details: { error: error instanceof Error ? error.message : String(error) },
      lastChecked: new Date().toISOString()
    };
  }
}

/**
 * Perform comprehensive health check
 */
export async function performHealthCheck(): Promise<SystemHealthStatus> {
  logger.info('Starting system health check');
  const startTime = Date.now();

  try {
    // Run all health checks
    const [
      databaseResult,
      externalAPIResults,
      memoryResult,
      rateLimitResult,
      sessionResult
    ] = await Promise.all([
      checkDatabase(),
      checkExternalAPIs(),
      Promise.resolve(checkMemoryUsage()),
      Promise.resolve(checkRateLimits()),
      Promise.resolve(checkSessions())
    ]);

    // Combine all results
    const services = [
      databaseResult,
      ...externalAPIResults,
      memoryResult,
      rateLimitResult,
      sessionResult
    ];

    // Calculate summary
    const summary = {
      healthy: services.filter(s => s.status === 'healthy').length,
      degraded: services.filter(s => s.status === 'degraded').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
      total: services.length
    };

    // Determine overall status
    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (summary.unhealthy > 0) {
      overall = 'unhealthy';
    } else if (summary.degraded > 0) {
      overall = 'degraded';
    }

    const result: SystemHealthStatus = {
      overall,
      services,
      summary,
      timestamp: new Date().toISOString()
    };

    const duration = Date.now() - startTime;
    logger.info('Health check completed', {
      overall,
      duration: `${duration}ms`,
      summary
    });

    return result;
  } catch (error) {
    logger.error('Health check failed', error instanceof Error ? error : new Error(String(error)));
    
    return {
      overall: 'unhealthy',
      services: [{
        service: 'health-check',
        status: 'unhealthy',
        message: 'Health check system failed',
        details: { error: error instanceof Error ? error.message : String(error) },
        lastChecked: new Date().toISOString()
      }],
      summary: { healthy: 0, degraded: 0, unhealthy: 1, total: 1 },
      timestamp: new Date().toISOString()
    };
  }
}
