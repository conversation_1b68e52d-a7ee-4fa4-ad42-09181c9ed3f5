import logger from './logger';

/**
 * Telegram Bot API types for webhook management
 */
interface WebhookInfo {
  url: string;
  has_custom_certificate: boolean;
  pending_update_count: number;
  last_error_date?: number;
  last_error_message?: string;
  max_connections?: number;
  allowed_updates?: string[];
}

interface TelegramApiResponse<T> {
  ok: boolean;
  result?: T;
  error_code?: number;
  description?: string;
}

/**
 * Set up Telegram bot webhook
 * @param botToken The bot token from @BotFather
 * @param webhookUrl The URL where Telegram should send updates
 * @returns Promise resolving to success status
 */
export async function setupTelegramWebhook(
  botToken: string,
  webhookUrl: string
): Promise<boolean> {
  try {
    logger.info('Setting up Telegram webhook', {
      webhookUrl,
      hasToken: !!botToken
    });

    // Set webhook
    const setWebhookUrl = `https://api.telegram.org/bot${botToken}/setWebhook`;
    const setWebhookPayload = {
      url: webhookUrl,
      max_connections: 40,
      allowed_updates: ['message', 'callback_query']
    };

    const setResponse = await fetch(setWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(setWebhookPayload),
    });

    const setResult: TelegramApiResponse<boolean> = await setResponse.json();

    if (setResult.ok) {
      logger.info('Telegram webhook set successfully', {
        webhookUrl,
        result: setResult.result
      });
      return true;
    } else {
      logger.error('Failed to set Telegram webhook', new Error(setResult.description), {
        errorCode: setResult.error_code,
        description: setResult.description,
        webhookUrl
      });
      return false;
    }
  } catch (error) {
    logger.error('Error setting up Telegram webhook', error instanceof Error ? error : new Error(String(error)), {
      webhookUrl
    });
    return false;
  }
}

/**
 * Get current webhook information
 * @param botToken The bot token from @BotFather
 * @returns Promise resolving to webhook info or null
 */
export async function getWebhookInfo(botToken: string): Promise<WebhookInfo | null> {
  try {
    logger.debug('Getting Telegram webhook info');

    const url = `https://api.telegram.org/bot${botToken}/getWebhookInfo`;
    const response = await fetch(url);
    const result: TelegramApiResponse<WebhookInfo> = await response.json();

    if (result.ok && result.result) {
      logger.info('Retrieved Telegram webhook info', {
        url: result.result.url,
        pendingUpdates: result.result.pending_update_count,
        hasError: !!result.result.last_error_message
      });
      return result.result;
    } else {
      logger.error('Failed to get Telegram webhook info', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return null;
    }
  } catch (error) {
    logger.error('Error getting Telegram webhook info', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

/**
 * Delete webhook (stop receiving updates)
 * @param botToken The bot token from @BotFather
 * @returns Promise resolving to success status
 */
export async function deleteWebhook(botToken: string): Promise<boolean> {
  try {
    logger.info('Deleting Telegram webhook');

    const url = `https://api.telegram.org/bot${botToken}/deleteWebhook`;
    const response = await fetch(url, { method: 'POST' });
    const result: TelegramApiResponse<boolean> = await response.json();

    if (result.ok) {
      logger.info('Telegram webhook deleted successfully');
      return true;
    } else {
      logger.error('Failed to delete Telegram webhook', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return false;
    }
  } catch (error) {
    logger.error('Error deleting Telegram webhook', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * Set bot commands (shown in the command menu)
 * @param botToken The bot token from @BotFather
 * @returns Promise resolving to success status
 */
export async function setBotCommands(botToken: string): Promise<boolean> {
  try {
    logger.info('Setting Telegram bot commands');

    const commands = [
      {
        command: 'start',
        description: 'Start the bot and see welcome message'
      },
      {
        command: 'latest',
        description: 'Get latest discovered tokens (e.g. /latest 5)'
      },
      {
        command: 'search',
        description: 'Search tokens by ticker or source (e.g. /search BTC)'
      },
      {
        command: 'help',
        description: 'Show help and available commands'
      }
    ];

    const url = `https://api.telegram.org/bot${botToken}/setMyCommands`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ commands }),
    });

    const result: TelegramApiResponse<boolean> = await response.json();

    if (result.ok) {
      logger.info('Telegram bot commands set successfully', { commandCount: commands.length });
      return true;
    } else {
      logger.error('Failed to set Telegram bot commands', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return false;
    }
  } catch (error) {
    logger.error('Error setting Telegram bot commands', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * Get bot information
 * @param botToken The bot token from @BotFather
 * @returns Promise resolving to bot info or null
 */
export async function getBotInfo(botToken: string) {
  try {
    logger.debug('Getting Telegram bot info');

    const url = `https://api.telegram.org/bot${botToken}/getMe`;
    const response = await fetch(url);
    const result = await response.json();

    if (result.ok && result.result) {
      logger.info('Retrieved Telegram bot info', {
        id: result.result.id,
        username: result.result.username,
        firstName: result.result.first_name,
        canJoinGroups: result.result.can_join_groups,
        canReadAllGroupMessages: result.result.can_read_all_group_messages,
        supportsInlineQueries: result.result.supports_inline_queries
      });
      return result.result;
    } else {
      logger.error('Failed to get Telegram bot info', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return null;
    }
  } catch (error) {
    logger.error('Error getting Telegram bot info', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

/**
 * Complete bot setup process
 * @param botToken The bot token from @BotFather
 * @param webhookUrl The URL where Telegram should send updates
 * @returns Promise resolving to setup success status
 */
export async function completeBotSetup(
  botToken: string,
  webhookUrl: string
): Promise<boolean> {
  try {
    logger.info('Starting complete bot setup', { webhookUrl });

    // Get bot info first to verify token
    const botInfo = await getBotInfo(botToken);
    if (!botInfo) {
      logger.error('Bot token verification failed');
      return false;
    }

    // Set up webhook
    const webhookSuccess = await setupTelegramWebhook(botToken, webhookUrl);
    if (!webhookSuccess) {
      logger.error('Webhook setup failed');
      return false;
    }

    // Set bot commands
    const commandsSuccess = await setBotCommands(botToken);
    if (!commandsSuccess) {
      logger.warn('Bot commands setup failed, but continuing...');
      // Don't fail the entire setup for this
    }

    logger.info('Bot setup completed successfully', {
      botUsername: botInfo.username,
      webhookUrl,
      commandsSet: commandsSuccess
    });

    return true;
  } catch (error) {
    logger.error('Error in complete bot setup', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * Validate bot configuration
 * @returns Object with configuration status
 */
export function validateBotConfiguration() {
  const botToken = process.env.NOTIFICATION_BOT_TOKEN;
  const chatId = process.env.NOTIFICATION_CHAT_ID;
  
  return {
    hasToken: !!botToken,
    hasChatId: !!chatId,
    isFullyConfigured: !!(botToken && chatId),
    missingVars: [
      !botToken && 'NOTIFICATION_BOT_TOKEN',
      !chatId && 'NOTIFICATION_CHAT_ID'
    ].filter(Boolean) as string[]
  };
}

/**
 * Generate webhook URL for the current deployment
 * @param baseUrl The base URL of the deployment (e.g. https://yourapp.vercel.app)
 * @returns The webhook URL
 */
export function generateWebhookUrl(baseUrl: string): string {
  // Remove trailing slash and add webhook path
  const cleanBaseUrl = baseUrl.replace(/\/$/, '');
  return `${cleanBaseUrl}/api/telegram/webhook`;
}