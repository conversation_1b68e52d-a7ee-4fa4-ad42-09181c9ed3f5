/**
 * Utility functions for handling retries with exponential backoff
 * and circuit breaking for external API calls
 */

import logger from '@/lib/logger';

/**
 * Configuration options for retry mechanism
 */
export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Initial delay in milliseconds */
  initialDelayMs: number;
  /** Maximum delay in milliseconds */
  maxDelayMs: number;
  /** Backoff factor (how quickly the delay increases) */
  backoffFactor: number;
  /** Whether to add jitter to the delay to prevent thundering herd */
  jitter: boolean;
  /** Function to determine if an error is retryable */
  isRetryable?: (error: unknown) => boolean;
}

/**
 * Default retry options
 */
export const defaultRetryOptions: RetryOptions = {
  maxRetries: 3,
  initialDelayMs: 1000,
  maxDelayMs: 10000,
  backoffFactor: 2,
  jitter: true,
  isRetryable: () => true, // By default, retry all errors
};

/**
 * Calculate the delay for the next retry attempt with exponential backoff
 * @param attempt Current attempt number (0-based)
 * @param options Retry options
 * @returns Delay in milliseconds
 */
export function calculateBackoffDelay(attempt: number, options: RetryOptions): number {
  const { initialDelayMs, maxDelayMs, backoffFactor, jitter } = options;

  // Calculate exponential backoff
  let delay = initialDelayMs * Math.pow(backoffFactor, attempt);

  // Apply maximum delay
  delay = Math.min(delay, maxDelayMs);

  // Add jitter if enabled (±20% randomness)
  if (jitter) {
    const jitterFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
    delay = Math.floor(delay * jitterFactor);
  }

  return delay;
}

/**
 * Execute a function with retry logic and exponential backoff
 * @param fn Function to execute
 * @param options Retry options
 * @returns Promise that resolves with the function result or rejects with the last error
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  // Merge options with defaults
  const retryOptions: RetryOptions = { ...defaultRetryOptions, ...options };
  const { maxRetries, isRetryable } = retryOptions;

  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // If this is a retry attempt, log it
      if (attempt > 0) {
        logger.info('Retry attempt', {
          attempt,
          maxRetries,
          operation: fn.name || 'anonymous'
        });
      }

      // Execute the function
      return await fn();
    } catch (error) {
      lastError = error;

      // Check if we've reached the maximum number of retries
      if (attempt >= maxRetries) {
        logger.warn('Maximum retry attempts reached', null, {
          attemptsMade: attempt + 1,
          maxRetries,
          operation: fn.name || 'anonymous'
        });
        break;
      }

      // Check if the error is retryable
      if (isRetryable && !isRetryable(error)) {
        logger.warn('Non-retryable error encountered', null, {
          attempt,
          operation: fn.name || 'anonymous',
          error: error instanceof Error ? error.message : String(error)
        });
        break;
      }

      // Calculate delay for next retry
      const delay = calculateBackoffDelay(attempt, retryOptions);

      logger.info('Retrying after delay', {
        attempt: attempt + 1,
        delayMs: delay,
        operation: fn.name || 'anonymous',
        error: error instanceof Error ? error.message : String(error)
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // If we get here, all retries failed
  throw lastError;
}

/**
 * Circuit breaker state
 */
interface CircuitBreakerState {
  /** Whether the circuit is open (failing) */
  isOpen: boolean;
  /** Timestamp when the circuit will be reset to half-open */
  resetTimeout: number;
  /** Number of consecutive failures */
  failureCount: number;
  /** Number of consecutive successes in half-open state */
  successCount: number;
}

/**
 * Circuit breaker options
 */
export interface CircuitBreakerOptions {
  /** Number of consecutive failures before opening the circuit */
  failureThreshold: number;
  /** Time in milliseconds to keep the circuit open before trying again */
  resetTimeoutMs: number;
  /** Number of consecutive successes needed to close the circuit */
  successThreshold: number;
  /** Function to determine if an error should count as a failure */
  isFailure?: (error: unknown) => boolean;
}

/**
 * Default circuit breaker options
 */
export const defaultCircuitBreakerOptions: CircuitBreakerOptions = {
  failureThreshold: 5,
  resetTimeoutMs: 30000, // 30 seconds
  successThreshold: 2,
  isFailure: () => true, // By default, count all errors as failures
};

/**
 * Circuit breaker registry to store circuit state for different services
 */
const circuitRegistry: Record<string, CircuitBreakerState> = {};

/**
 * Execute a function with circuit breaker pattern
 * @param serviceName Unique identifier for the service
 * @param fn Function to execute
 * @param options Circuit breaker options
 * @returns Promise that resolves with the function result or rejects with the error
 */
export async function withCircuitBreaker<T>(
  serviceName: string,
  fn: () => Promise<T>,
  options: Partial<CircuitBreakerOptions> = {}
): Promise<T> {
  // Merge options with defaults
  const circuitOptions: CircuitBreakerOptions = {
    ...defaultCircuitBreakerOptions,
    ...options
  };

  // Initialize circuit state if it doesn't exist
  if (!circuitRegistry[serviceName]) {
    circuitRegistry[serviceName] = {
      isOpen: false,
      resetTimeout: 0,
      failureCount: 0,
      successCount: 0,
    };
  }

  const circuit = circuitRegistry[serviceName];
  const now = Date.now();

  // Check if circuit is open
  if (circuit.isOpen) {
    // Check if reset timeout has elapsed
    if (now >= circuit.resetTimeout) {
      // Move to half-open state
      logger.info('Circuit half-open, allowing test request', { serviceName });
      circuit.isOpen = false;
    } else {
      // Circuit is still open, fast fail
      const remainingTime = Math.ceil((circuit.resetTimeout - now) / 1000);
      logger.warn('Circuit open, fast failing request', null, {
        serviceName,
        remainingSeconds: remainingTime
      });
      throw new Error(`Circuit breaker open for ${serviceName} (${remainingTime}s remaining)`);
    }
  }

  try {
    // Execute the function
    const result = await fn();

    // Handle success
    if (circuit.failureCount > 0) {
      // We're in a recovery state (half-open or previously failing)
      circuit.successCount++;

      if (circuit.successCount >= circuitOptions.successThreshold) {
        // Reset the circuit after enough consecutive successes
        logger.info('Circuit closed after consecutive successes', {
          serviceName,
          successCount: circuit.successCount,
          threshold: circuitOptions.successThreshold
        });
        circuit.failureCount = 0;
        circuit.successCount = 0;
      } else {
        logger.info('Success in half-open circuit', {
          serviceName,
          successCount: circuit.successCount,
          threshold: circuitOptions.successThreshold
        });
      }
    }

    return result;
  } catch (error) {
    // Check if this error counts as a failure
    if (!circuitOptions.isFailure || circuitOptions.isFailure(error)) {
      // Increment failure count
      circuit.failureCount++;
      circuit.successCount = 0;

      logger.warn('Request failed in circuit', null, {
        serviceName,
        failureCount: circuit.failureCount,
        threshold: circuitOptions.failureThreshold,
        error: error instanceof Error ? error.message : String(error)
      });

      // Check if we've reached the failure threshold
      if (circuit.failureCount >= circuitOptions.failureThreshold) {
        // Open the circuit
        circuit.isOpen = true;
        circuit.resetTimeout = now + circuitOptions.resetTimeoutMs;

        logger.error('Circuit opened after consecutive failures', null, {
          serviceName,
          failureCount: circuit.failureCount,
          resetTimeoutMs: circuitOptions.resetTimeoutMs
        });
      }
    }

    // Re-throw the error
    throw error;
  }
}
