/**
 * Utility functions for database operations
 */

import logger from '@/lib/logger';
import { AppError, ErrorCategory } from '@/lib/errorUtils';
import { supabase } from '@/lib/supabase';

/**
 * Pagination parameters
 */
export interface PaginationParams {
  /** Page number (1-based) */
  page?: number;
  /** Number of items per page */
  pageSize?: number;
  /** Sort column */
  sortBy?: string;
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
}

/**
 * Default pagination parameters
 */
export const DEFAULT_PAGINATION: Required<PaginationParams> = {
  page: 1,
  pageSize: 20,
  sortBy: 'created_at',
  sortDirection: 'desc',
};

/**
 * Pagination result
 */
export interface PaginatedResult<T> {
  /** Items for the current page */
  data: T[];
  /** Total number of items */
  total: number;
  /** Current page number */
  page: number;
  /** Number of items per page */
  pageSize: number;
  /** Total number of pages */
  totalPages: number;
  /** Whether there is a next page */
  hasNextPage: boolean;
  /** Whether there is a previous page */
  hasPreviousPage: boolean;
}

/**
 * Create a paginated query
 * @param query Supabase query
 * @param params Pagination parameters
 * @returns Paginated query
 */
export function paginateQuery(
  query: any, // eslint-disable-line @typescript-eslint/no-explicit-any
  params: PaginationParams = {}
): any { // eslint-disable-line @typescript-eslint/no-explicit-any
  const { page, pageSize, sortBy, sortDirection } = {
    ...DEFAULT_PAGINATION,
    ...params,
  };

  // Calculate range for pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Apply sorting if specified
  if (sortBy) {
    query = query.order(sortBy, { ascending: sortDirection === 'asc' });
  }

  // Apply pagination
  return query.range(from, to);
}

/**
 * Execute a paginated query and return the result
 * @param query Supabase query
 * @param countQuery Supabase count query
 * @param params Pagination parameters
 * @returns Paginated result
 */
export async function executePaginatedQuery<T>(
  query: any, // eslint-disable-line @typescript-eslint/no-explicit-any
  countQuery: any, // eslint-disable-line @typescript-eslint/no-explicit-any
  params: PaginationParams = {}
): Promise<PaginatedResult<T>> {
  const { page, pageSize } = {
    ...DEFAULT_PAGINATION,
    ...params,
  };

  try {
    // Execute the paginated query
    const { data, error } = await paginateQuery(query, params);

    if (error) {
      throw new AppError(
        'Database query failed',
        ErrorCategory.INTERNAL,
        'db_query_error',
        { error: error.message }
      );
    }

    // Get the total count
    const { count, error: countError } = await countQuery;

    if (countError) {
      throw new AppError(
        'Database count query failed',
        ErrorCategory.INTERNAL,
        'db_count_error',
        { error: countError.message }
      );
    }

    // Calculate pagination metadata
    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      data: data || [],
      total,
      page,
      pageSize,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Error executing paginated query', error instanceof Error ? error : new Error(String(error)));
    throw new AppError(
      'Failed to execute database query',
      ErrorCategory.INTERNAL,
      'db_query_error',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Get Supabase client with validation
 * @returns Supabase client
 * @throws AppError if not initialized
 */
export function getSupabaseClient() {
  if (!supabase) {
    throw new AppError(
      'Supabase client not initialized',
      ErrorCategory.INTERNAL,
      'supabase_not_initialized',
      {
        missingEnvVars: [
          !process.env.NEXT_PUBLIC_SUPABASE_URL ? 'NEXT_PUBLIC_SUPABASE_URL' : null,
          !process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SUPABASE_SERVICE_ROLE_KEY' : null,
          !process.env.SUPABASE_SERVICE_KEY ? 'SUPABASE_SERVICE_KEY' : null,
        ].filter(Boolean)
      }
    );
  }
  return supabase;
}

/**
 * Execute a database operation with error handling
 * @param operation Function that performs the database operation
 * @returns Result of the operation
 */
export async function withDb<T>(
  operation: () => Promise<T>
): Promise<T> {
  try {
    // Ensure Supabase is initialized
    getSupabaseClient();

    // Execute the operation
    return await operation();
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Database operation failed', error instanceof Error ? error : new Error(String(error)));
    throw new AppError(
      'Database operation failed',
      ErrorCategory.INTERNAL,
      'db_operation_error',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}
