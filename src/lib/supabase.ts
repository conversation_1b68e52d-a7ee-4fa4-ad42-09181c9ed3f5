import { createClient } from '@supabase/supabase-js';
import { Database } from './database.types';
import logger from './logger';

// These environment variables need to be set in .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || '';

// Helper function to check if Supabase is properly configured
export function isSupabaseConfigured(): boolean {
  const isConfigured = Boolean(
    supabaseUrl && supabaseKey
  );

  if (isConfigured) {
    logger.info('Supabase is properly configured', {
      url: supabaseUrl.substring(0, 15) + '...',
      keyLength: supabaseKey.length
    });
  } else {
    logger.warn('Supabase is not configured', null, {
      missingEnvVars: [
        !supabaseUrl ? 'NEXT_PUBLIC_SUPABASE_URL' : null,
        !supabaseKey ? 'SUPABASE_SERVICE_ROLE_KEY or SUPABASE_SERVICE_KEY' : null
      ].filter(Boolean),
      action: 'Set required environment variables for Supabase'
    });
  }

  return isConfigured;
}

// Create a single supabase client for the entire app
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: true,
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'x-application-name': 'binance-bot',
    },
  },
});

// Initialize Supabase and check connection
(async function initializeSupabase() {
  try {
    if (isSupabaseConfigured()) {
      // Test the connection
      const { error } = await supabase.from('tokens').select('count', { count: 'exact', head: true });

      if (error) {
        logger.error('Failed to connect to Supabase', new Error(error.message), {
          code: error.code,
          details: error.details,
          hint: error.hint
        });
      } else {
        logger.info('Successfully connected to Supabase', {
          message: 'Database connection established'
        });
      }
    }
  } catch (error) {
    logger.error('Error initializing Supabase', error instanceof Error ? error : new Error(String(error)));
  }
})();
