/**
 * Logger utility for structured logging with different levels and contextual information
 *
 * Features:
 * - Log levels (debug, info, warn, error)
 * - Request ID tracking
 * - Contextual information
 * - Stack traces for errors
 * - Timestamp
 * - Source information (file, function)
 */

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

// Log entry interface
export interface LogEntry {
  timestamp?: string;
  level: LogLevel;
  message: string;
  requestId?: string;
  context?: Record<string, unknown>;
  error?: Error;
  source?: {
    file?: string;
    function?: string;
  };
}

// Logger configuration
export interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableRequestId: boolean;
  includeTimestamp: boolean;
}

// Default configuration
const defaultConfig: LoggerConfig = {
  minLevel: LogLevel.INFO,
  enableConsole: true,
  enableRequestId: true,
  includeTimestamp: true,
};

// Global request ID
let currentRequestId: string | null = null;

/**
 * Set the current request ID for the logger
 * @param requestId The request ID to set
 */
export function setRequestId(requestId: string): void {
  currentRequestId = requestId;
}

/**
 * Get the current request ID
 * @returns The current request ID or null if not set
 */
export function getRequestId(): string | null {
  return currentRequestId;
}

/**
 * Generate a new request ID
 * @returns A new request ID
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Clear the current request ID
 */
export function clearRequestId(): void {
  currentRequestId = null;
}

// Logger class
class Logger {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Log a message at the specified level
   * @param level Log level
   * @param message Message to log
   * @param context Additional context
   * @param error Error object (for error logs)
   */
  private log(level: LogLevel, message: string, context?: Record<string, unknown>, error?: Error): void {
    // Skip if log level is below minimum
    if (this.shouldSkipLog(level)) {
      return;
    }

    // Create log entry
    const entry: LogEntry = {
      timestamp: this.config.includeTimestamp ? new Date().toISOString() : undefined,
      level,
      message,
      context,
      error,
    };

    // Add request ID if enabled
    if (this.config.enableRequestId && currentRequestId) {
      entry.requestId = currentRequestId;
    }

    // Add source information if available
    if (error && error.stack) {
      const stackInfo = this.parseStackTrace(error.stack);
      if (stackInfo) {
        entry.source = stackInfo;
      }
    }

    // Output to console if enabled
    if (this.config.enableConsole) {
      this.outputToConsole(entry);
    }
  }

  /**
   * Check if the log should be skipped based on level
   * @param level Log level
   * @returns True if the log should be skipped
   */
  private shouldSkipLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR];
    const minLevelIndex = levels.indexOf(this.config.minLevel);
    const currentLevelIndex = levels.indexOf(level);

    return currentLevelIndex < minLevelIndex;
  }

  /**
   * Parse stack trace to extract source information
   * @param stack Stack trace string
   * @returns Source information
   */
  private parseStackTrace(stack: string): { file?: string; function?: string } | null {
    const lines = stack.split('\n');

    // Skip the first line (error message) and the second line (this function)
    if (lines.length < 3) {
      return null;
    }

    // Parse the third line for source information
    const match = lines[2].match(/at\s+(?:(.+?)\s+\()?(?:(.+?):(\d+):(\d+))\)?/);
    if (!match) {
      return null;
    }

    const [, fnName, fileName] = match;
    return {
      file: fileName,
      function: fnName || 'anonymous',
    };
  }

  /**
   * Output log entry to console
   * @param entry Log entry
   */
  private outputToConsole(entry: LogEntry): void {
    const { level, message, requestId, context, error, source, timestamp } = entry;

    // Format the log message
    let formattedMessage = '';

    // Add timestamp
    if (timestamp) {
      formattedMessage += `[${timestamp}] `;
    }

    // Add log level
    formattedMessage += `[${level.toUpperCase()}]`;

    // Add request ID
    if (requestId) {
      formattedMessage += ` [${requestId}]`;
    }

    // Add source information
    if (source) {
      if (source.file && source.function) {
        formattedMessage += ` [${source.function} in ${source.file}]`;
      } else if (source.file) {
        formattedMessage += ` [${source.file}]`;
      } else if (source.function) {
        formattedMessage += ` [${source.function}]`;
      }
    }

    formattedMessage += `: ${message}`;

    // Output to console based on level
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
    }

    // Log additional context if available
    if (context && Object.keys(context).length > 0) {
      console.log('Context:', context);
    }

    // Log error stack trace if available
    if (error && error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }

  /**
   * Log a debug message
   * @param message Message to log
   * @param context Additional context
   */
  debug(message: string, context?: Record<string, unknown>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log an info message
   * @param message Message to log
   * @param context Additional context
   */
  info(message: string, context?: Record<string, unknown>): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log a warning message
   * @param message Message to log
   * @param error Error object (optional)
   * @param context Additional context (optional)
   */
  warn(message: string, error?: Error | null, context?: Record<string, unknown>): void {
    if (error) {
      this.log(LogLevel.WARN, message, context, error);
    } else {
      this.log(LogLevel.WARN, message, context);
    }
  }

  /**
   * Log an error message
   * @param message Message to log
   * @param error Error object (optional)
   * @param context Additional context (optional)
   */
  error(message: string, error?: Error | null, context?: Record<string, unknown>): void {
    if (error) {
      this.log(LogLevel.ERROR, message, context, error);
    } else {
      this.log(LogLevel.ERROR, message, context);
    }
  }

  /**
   * Update logger configuration
   * @param config New configuration
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Create and export default logger instance
const logger = new Logger();
export default logger;
