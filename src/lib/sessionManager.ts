/**
 * Session management utilities for external services
 * Handles session refresh and validation for long-running services
 */

import logger from './logger';

export interface SessionInfo {
  serviceName: string;
  sessionData: string;
  lastValidated: number;
  expiresAt?: number;
  refreshCount: number;
}

// Session registry to track active sessions
const sessionRegistry: Record<string, SessionInfo> = {};

/**
 * Session validation options
 */
export interface SessionValidationOptions {
  /** Maximum age of session in milliseconds before requiring validation */
  maxAge?: number;
  /** Function to validate if session is still active */
  validator?: (sessionData: string) => Promise<boolean>;
  /** Function to refresh session if needed */
  refresher?: (oldSessionData: string) => Promise<string>;
  /** Maximum number of refresh attempts */
  maxRefreshAttempts?: number;
}

/**
 * Default session validation options
 */
const DEFAULT_SESSION_OPTIONS: Required<SessionValidationOptions> = {
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  validator: async () => true, // Default: always valid
  refresher: async (sessionData) => sessionData, // Default: no refresh
  maxRefreshAttempts: 3
};

/**
 * Register a session for management
 */
export function registerSession(
  serviceName: string,
  sessionData: string,
  expiresAt?: number
): void {
  sessionRegistry[serviceName] = {
    serviceName,
    sessionData,
    lastValidated: Date.now(),
    expiresAt,
    refreshCount: 0
  };
  
  logger.info('Session registered', {
    serviceName,
    expiresAt: expiresAt ? new Date(expiresAt).toISOString() : 'never'
  });
}

/**
 * Get current session data for a service
 */
export function getSession(serviceName: string): string | null {
  const session = sessionRegistry[serviceName];
  return session ? session.sessionData : null;
}

/**
 * Check if session needs validation or refresh
 */
export function needsValidation(
  serviceName: string,
  options: Partial<SessionValidationOptions> = {}
): boolean {
  const session = sessionRegistry[serviceName];
  if (!session) return false;
  
  const opts = { ...DEFAULT_SESSION_OPTIONS, ...options };
  const now = Date.now();
  
  // Check if session has expired
  if (session.expiresAt && now >= session.expiresAt) {
    logger.warn('Session expired', null, {
      serviceName,
      expiresAt: new Date(session.expiresAt).toISOString()
    });
    return true;
  }
  
  // Check if session is too old
  if (now - session.lastValidated > opts.maxAge) {
    logger.info('Session needs validation due to age', {
      serviceName,
      ageHours: ((now - session.lastValidated) / (60 * 60 * 1000)).toFixed(2)
    });
    return true;
  }
  
  return false;
}

/**
 * Validate and potentially refresh a session
 */
export async function validateSession(
  serviceName: string,
  options: Partial<SessionValidationOptions> = {}
): Promise<boolean> {
  const session = sessionRegistry[serviceName];
  if (!session) {
    logger.warn('No session found for validation', null, { serviceName });
    return false;
  }
  
  const opts = { ...DEFAULT_SESSION_OPTIONS, ...options };
  
  try {
    // First, try to validate the current session
    logger.debug('Validating session', { serviceName });
    const isValid = await opts.validator(session.sessionData);
    
    if (isValid) {
      // Update last validated time
      session.lastValidated = Date.now();
      logger.debug('Session validation successful', { serviceName });
      return true;
    }
    
    // Session is invalid, try to refresh if possible
    if (session.refreshCount >= opts.maxRefreshAttempts) {
      logger.error('Session refresh attempts exceeded', new Error('Max refresh attempts reached'), {
        serviceName,
        refreshCount: session.refreshCount,
        maxAttempts: opts.maxRefreshAttempts
      });
      return false;
    }
    
    logger.info('Session invalid, attempting refresh', {
      serviceName,
      refreshAttempt: session.refreshCount + 1
    });
    
    const newSessionData = await opts.refresher(session.sessionData);
    
    if (newSessionData && newSessionData !== session.sessionData) {
      // Update session with new data
      session.sessionData = newSessionData;
      session.lastValidated = Date.now();
      session.refreshCount++;
      
      logger.info('Session refreshed successfully', {
        serviceName,
        refreshCount: session.refreshCount
      });
      
      return true;
    } else {
      logger.warn('Session refresh returned same or empty data', null, {
        serviceName,
        hasNewData: !!newSessionData,
        isSameData: newSessionData === session.sessionData
      });
      return false;
    }
    
  } catch (error) {
    logger.error('Error during session validation/refresh', 
      error instanceof Error ? error : new Error(String(error)), 
      { serviceName }
    );
    return false;
  }
}

/**
 * Remove a session from management
 */
export function unregisterSession(serviceName: string): void {
  if (sessionRegistry[serviceName]) {
    delete sessionRegistry[serviceName];
    logger.info('Session unregistered', { serviceName });
  }
}

/**
 * Get session status for monitoring
 */
export function getSessionStatus(serviceName: string): {
  exists: boolean;
  lastValidated?: string;
  expiresAt?: string;
  refreshCount?: number;
  needsValidation?: boolean;
} {
  const session = sessionRegistry[serviceName];
  
  if (!session) {
    return { exists: false };
  }
  
  return {
    exists: true,
    lastValidated: new Date(session.lastValidated).toISOString(),
    expiresAt: session.expiresAt ? new Date(session.expiresAt).toISOString() : undefined,
    refreshCount: session.refreshCount,
    needsValidation: needsValidation(serviceName)
  };
}

/**
 * Get all session statuses for monitoring
 */
export function getAllSessionStatuses(): Record<string, ReturnType<typeof getSessionStatus>> {
  const statuses: Record<string, ReturnType<typeof getSessionStatus>> = {};
  
  for (const serviceName of Object.keys(sessionRegistry)) {
    statuses[serviceName] = getSessionStatus(serviceName);
  }
  
  return statuses;
}

/**
 * Wrapper function to ensure session is valid before executing an operation
 */
export async function withValidSession<T>(
  serviceName: string,
  operation: (sessionData: string) => Promise<T>,
  options: Partial<SessionValidationOptions> = {}
): Promise<T> {
  // Check if session needs validation
  if (needsValidation(serviceName, options)) {
    const isValid = await validateSession(serviceName, options);
    if (!isValid) {
      throw new Error(`Session validation failed for ${serviceName}`);
    }
  }
  
  const sessionData = getSession(serviceName);
  if (!sessionData) {
    throw new Error(`No valid session found for ${serviceName}`);
  }
  
  return await operation(sessionData);
}

/**
 * Cleanup expired sessions
 */
export function cleanupExpiredSessions(): void {
  const now = Date.now();
  const expiredServices: string[] = [];
  
  for (const [serviceName, session] of Object.entries(sessionRegistry)) {
    if (session.expiresAt && now >= session.expiresAt) {
      expiredServices.push(serviceName);
    }
  }
  
  for (const serviceName of expiredServices) {
    unregisterSession(serviceName);
    logger.info('Cleaned up expired session', { serviceName });
  }
  
  if (expiredServices.length > 0) {
    logger.info('Session cleanup completed', { 
      cleanedCount: expiredServices.length,
      remainingCount: Object.keys(sessionRegistry).length
    });
  }
}
