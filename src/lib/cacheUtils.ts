/**
 * Utility functions for caching API responses and expensive operations
 */

import logger from '@/lib/logger';

/**
 * Cache entry with expiration
 */
interface CacheEntry<T> {
  value: T;
  expiry: number;
}

/**
 * In-memory cache store
 */
const memoryCache: Map<string, CacheEntry<unknown>> = new Map();

/**
 * Cache options
 */
export interface CacheOptions {
  /** Time-to-live in milliseconds */
  ttl: number;
  /** Whether to return stale data while refreshing */
  staleWhileRevalidate?: boolean;
  /** Custom key generator function */
  keyGenerator?: (...args: unknown[]) => string;
}

/**
 * Default cache options
 */
export const defaultCacheOptions: CacheOptions = {
  ttl: 60 * 1000, // 1 minute
  staleWhileRevalidate: false,
};

/**
 * Generate a cache key from function arguments
 * @param prefix Key prefix
 * @param args Function arguments
 * @returns Cache key
 */
export function generateCacheKey(prefix: string, args: unknown[]): string {
  try {
    // Convert arguments to a stable string representation
    const argsStr = args.map(arg => {
      if (arg === null || arg === undefined) {
        return String(arg);
      }
      if (typeof arg === 'object') {
        try {
          return JSON.stringify(arg);
        } catch {
          return Object.prototype.toString.call(arg);
        }
      }
      return String(arg);
    }).join('::');
    
    return `${prefix}:${argsStr}`;
  } catch (error) {
    // If key generation fails, use a fallback
    logger.warn('Error generating cache key', null, {
      prefix,
      error: String(error)
    });
    return `${prefix}:${Date.now()}`;
  }
}

/**
 * Check if a cache entry is expired
 * @param entry Cache entry
 * @returns True if expired
 */
function isExpired<T>(entry: CacheEntry<T>): boolean {
  return Date.now() > entry.expiry;
}

/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or undefined if not found or expired
 */
export function getCached<T>(key: string): T | undefined {
  const entry = memoryCache.get(key) as CacheEntry<T> | undefined;
  
  if (!entry) {
    return undefined;
  }
  
  if (isExpired(entry)) {
    // Remove expired entry
    memoryCache.delete(key);
    return undefined;
  }
  
  return entry.value as T;
}

/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time-to-live in milliseconds
 */
export function setCached<T>(key: string, value: T, ttl: number): void {
  const expiry = Date.now() + ttl;
  memoryCache.set(key, { value, expiry });
}

/**
 * Clear the entire cache or entries with a specific prefix
 * @param prefix Optional key prefix to clear
 */
export function clearCache(prefix?: string): void {
  if (!prefix) {
    memoryCache.clear();
    logger.debug('Cleared entire cache');
    return;
  }
  
  // Clear entries with the specified prefix
  const keysToDelete: string[] = [];
  
  for (const key of memoryCache.keys()) {
    if (key.startsWith(`${prefix}:`)) {
      keysToDelete.push(key);
    }
  }
  
  keysToDelete.forEach(key => memoryCache.delete(key));
  logger.debug('Cleared cache entries with prefix', { prefix, count: keysToDelete.length });
}

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export function getCacheStats(): { total: number; expired: number; active: number } {
  let expired = 0;
  let active = 0;
  
  for (const entry of memoryCache.values()) {
    if (isExpired(entry)) {
      expired++;
    } else {
      active++;
    }
  }
  
  return {
    total: memoryCache.size,
    expired,
    active,
  };
}

/**
 * Create a cached version of an async function
 * @param fn Function to cache
 * @param options Cache options
 * @returns Cached function
 */
export function withCache<T, Args extends unknown[]>(
  fn: (...args: Args) => Promise<T>,
  options: Partial<CacheOptions> = {}
): (...args: Args) => Promise<T> {
  const cacheOptions: CacheOptions = { ...defaultCacheOptions, ...options };
  const fnName = fn.name || 'anonymous';
  
  return async (...args: Args): Promise<T> => {
    // Generate cache key
    const key = cacheOptions.keyGenerator
      ? cacheOptions.keyGenerator(...args)
      : generateCacheKey(fnName, args);
    
    // Check cache
    const cached = getCached<T>(key);
    
    if (cached !== undefined) {
      logger.debug('Cache hit', { key, function: fnName });
      return cached;
    }
    
    logger.debug('Cache miss', { key, function: fnName });
    
    // Execute function and cache result
    const result = await fn(...args);
    setCached(key, result, cacheOptions.ttl);
    
    return result;
  };
}

/**
 * Create a cached version of an async function with stale-while-revalidate behavior
 * @param fn Function to cache
 * @param options Cache options
 * @returns Cached function
 */
export function withStaleCache<T, Args extends unknown[]>(
  fn: (...args: Args) => Promise<T>,
  options: Partial<CacheOptions> = {}
): (...args: Args) => Promise<T> {
  const cacheOptions: CacheOptions = { 
    ...defaultCacheOptions, 
    staleWhileRevalidate: true,
    ...options 
  };
  const fnName = fn.name || 'anonymous';
  
  return async (...args: Args): Promise<T> => {
    // Generate cache key
    const key = cacheOptions.keyGenerator
      ? cacheOptions.keyGenerator(...args)
      : generateCacheKey(fnName, args);
    
    // Get the cached entry directly to check if it's stale
    const entry = memoryCache.get(key) as CacheEntry<T> | undefined;
    
    if (entry) {
      const isStale = isExpired(entry);
      
      if (!isStale) {
        // Fresh cache hit
        logger.debug('Cache hit (fresh)', { key, function: fnName });
        return entry.value;
      }
      
      if (cacheOptions.staleWhileRevalidate) {
        // Stale cache hit, revalidate in background
        logger.debug('Cache hit (stale)', { key, function: fnName });
        
        // Start revalidation in background
        Promise.resolve().then(async () => {
          try {
            logger.debug('Revalidating stale cache', { key, function: fnName });
            const freshResult = await fn(...args);
            setCached(key, freshResult, cacheOptions.ttl);
            logger.debug('Cache revalidated', { key, function: fnName });
          } catch (error) {
            logger.error('Error revalidating cache', error instanceof Error ? error : new Error(String(error)), {
              key,
              function: fnName
            });
          }
        });
        
        // Return stale data immediately
        return entry.value;
      }
    }
    
    // Cache miss or stale without revalidation
    logger.debug('Cache miss', { key, function: fnName });
    
    // Execute function and cache result
    const result = await fn(...args);
    setCached(key, result, cacheOptions.ttl);
    
    return result;
  };
}
