/**
 * Rate limiting utilities for external API calls
 * Implements token bucket algorithm with per-service limits
 */

import logger from './logger';

export interface RateLimitConfig {
  /** Maximum number of requests per window */
  maxRequests: number;
  /** Time window in milliseconds */
  windowMs: number;
  /** Minimum delay between requests in milliseconds */
  minDelayMs?: number;
  /** Maximum burst size (defaults to maxRequests) */
  burstSize?: number;
}

export interface RateLimitState {
  tokens: number;
  lastRefill: number;
  requestQueue: Array<{
    resolve: () => void;
    reject: (error: Error) => void;
    timestamp: number;
  }>;
  processing: boolean;
}

// Default rate limit configurations for different services
export const DEFAULT_RATE_LIMITS: Record<string, RateLimitConfig> = {
  'twitter-api': {
    maxRequests: 300, // Twitter API v2 limit
    windowMs: 15 * 60 * 1000, // 15 minutes
    minDelayMs: 1000, // 1 second between requests
    burstSize: 10
  },
  'telegram-api': {
    maxRequests: 20, // Conservative limit for Telegram
    windowMs: 60 * 1000, // 1 minute
    minDelayMs: 3000, // 3 seconds between requests
    burstSize: 5
  },
  'firecrawl-api': {
    maxRequests: 100, // Firecrawl free tier
    windowMs: 60 * 60 * 1000, // 1 hour
    minDelayMs: 2000, // 2 seconds between requests
    burstSize: 3
  },
  'openrouter-api': {
    maxRequests: 200, // OpenRouter limit
    windowMs: 60 * 1000, // 1 minute
    minDelayMs: 500, // 500ms between requests
    burstSize: 10
  }
};

// Global rate limit registry
const rateLimitRegistry: Record<string, RateLimitState> = {};

/**
 * Initialize rate limit state for a service
 */
function initializeRateLimit(serviceName: string, config: RateLimitConfig): RateLimitState {
  const burstSize = config.burstSize || config.maxRequests;
  
  return {
    tokens: burstSize,
    lastRefill: Date.now(),
    requestQueue: [],
    processing: false
  };
}

/**
 * Refill tokens based on elapsed time
 */
function refillTokens(state: RateLimitState, config: RateLimitConfig): void {
  const now = Date.now();
  const elapsed = now - state.lastRefill;
  const burstSize = config.burstSize || config.maxRequests;
  
  if (elapsed >= config.windowMs) {
    // Full refill if window has passed
    state.tokens = burstSize;
    state.lastRefill = now;
    logger.debug('Rate limit tokens fully refilled', {
      tokens: state.tokens,
      burstSize
    });
  } else {
    // Gradual refill based on elapsed time
    const refillRate = burstSize / config.windowMs;
    const tokensToAdd = Math.floor(elapsed * refillRate);
    
    if (tokensToAdd > 0) {
      state.tokens = Math.min(burstSize, state.tokens + tokensToAdd);
      state.lastRefill = now;
      logger.debug('Rate limit tokens partially refilled', {
        tokensAdded: tokensToAdd,
        currentTokens: state.tokens,
        burstSize
      });
    }
  }
}

/**
 * Process the request queue
 */
async function processQueue(serviceName: string, config: RateLimitConfig): Promise<void> {
  const state = rateLimitRegistry[serviceName];
  
  if (state.processing || state.requestQueue.length === 0) {
    return;
  }
  
  state.processing = true;
  
  try {
    while (state.requestQueue.length > 0) {
      refillTokens(state, config);
      
      if (state.tokens <= 0) {
        // Wait for next refill opportunity
        const waitTime = Math.max(1000, config.minDelayMs || 1000);
        logger.debug('Rate limit exceeded, waiting for refill', {
          serviceName,
          waitTime,
          queueLength: state.requestQueue.length
        });
        
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }
      
      // Process next request
      const request = state.requestQueue.shift();
      if (!request) break;
      
      // Check if request has timed out (older than 5 minutes)
      if (Date.now() - request.timestamp > 5 * 60 * 1000) {
        request.reject(new Error('Rate limit request timed out'));
        continue;
      }
      
      state.tokens--;
      logger.debug('Rate limit token consumed', {
        serviceName,
        remainingTokens: state.tokens,
        queueLength: state.requestQueue.length
      });
      
      request.resolve();
      
      // Apply minimum delay between requests
      if (config.minDelayMs && state.requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, config.minDelayMs));
      }
    }
  } finally {
    state.processing = false;
  }
}

/**
 * Wait for rate limit permission
 */
export async function waitForRateLimit(
  serviceName: string,
  customConfig?: Partial<RateLimitConfig>
): Promise<void> {
  // Get or create rate limit configuration
  const defaultConfig = DEFAULT_RATE_LIMITS[serviceName];
  if (!defaultConfig && !customConfig) {
    logger.warn('No rate limit configuration found for service', null, {
      serviceName,
      action: 'Using default configuration'
    });
  }
  
  const config: RateLimitConfig = {
    ...defaultConfig,
    ...customConfig,
    maxRequests: customConfig?.maxRequests || defaultConfig?.maxRequests || 100,
    windowMs: customConfig?.windowMs || defaultConfig?.windowMs || 60000
  };
  
  // Initialize rate limit state if not exists
  if (!rateLimitRegistry[serviceName]) {
    rateLimitRegistry[serviceName] = initializeRateLimit(serviceName, config);
    logger.info('Initialized rate limiting for service', {
      serviceName,
      config
    });
  }
  
  const state = rateLimitRegistry[serviceName];
  
  // Check if we can proceed immediately
  refillTokens(state, config);
  
  if (state.tokens > 0) {
    state.tokens--;
    logger.debug('Rate limit permission granted immediately', {
      serviceName,
      remainingTokens: state.tokens
    });
    return;
  }
  
  // Add to queue and wait
  return new Promise((resolve, reject) => {
    state.requestQueue.push({
      resolve,
      reject,
      timestamp: Date.now()
    });
    
    logger.debug('Added to rate limit queue', {
      serviceName,
      queuePosition: state.requestQueue.length,
      remainingTokens: state.tokens
    });
    
    // Start processing queue
    processQueue(serviceName, config).catch(error => {
      logger.error('Error processing rate limit queue', error instanceof Error ? error : new Error(String(error)), {
        serviceName
      });
    });
  });
}

/**
 * Get current rate limit status for a service
 */
export function getRateLimitStatus(serviceName: string): {
  tokens: number;
  queueLength: number;
  lastRefill: number;
} | null {
  const state = rateLimitRegistry[serviceName];
  if (!state) return null;
  
  return {
    tokens: state.tokens,
    queueLength: state.requestQueue.length,
    lastRefill: state.lastRefill
  };
}

/**
 * Reset rate limit for a service (useful for testing)
 */
export function resetRateLimit(serviceName: string): void {
  delete rateLimitRegistry[serviceName];
  logger.info('Rate limit reset for service', { serviceName });
}

/**
 * Wrapper function to apply rate limiting to any async function
 */
export async function withRateLimit<T>(
  serviceName: string,
  fn: () => Promise<T>,
  customConfig?: Partial<RateLimitConfig>
): Promise<T> {
  await waitForRateLimit(serviceName, customConfig);
  return await fn();
}
