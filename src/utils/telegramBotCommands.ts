import { Token } from '@/lib/types';
import { getLatestTokens, searchTokens } from '@/lib/supabaseService';
import logger from '@/lib/logger';

// Telegram Bot API types
export interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
}

export interface TelegramMessage {
  message_id: number;
  from?: TelegramUser;
  chat: TelegramChat;
  date: number;
  text?: string;
  entities?: TelegramMessageEntity[];
}

export interface TelegramUser {
  id: number;
  is_bot: boolean;
  first_name: string;
  last_name?: string;
  username?: string;
}

export interface TelegramChat {
  id: number;
  type: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  title?: string;
}

export interface TelegramMessageEntity {
  type: string;
  offset: number;
  length: number;
}

export interface BotResponse {
  text: string;
  parse_mode?: 'HTML' | 'Markdown';
  disable_web_page_preview?: boolean;
}

/**
 * Process incoming Telegram bot commands and generate appropriate responses
 */
export async function processCommand(update: TelegramUpdate): Promise<BotResponse | null> {
  if (!update.message?.text) {
    return null;
  }

  const text = update.message.text.trim();
  const chatId = update.message.chat.id;
  const userId = update.message.from?.id;
  const username = update.message.from?.username || update.message.from?.first_name || 'User';

  logger.info('Processing bot command', {
    chatId,
    userId,
    username,
    command: text.substring(0, 50) // Log first 50 chars for debugging
  });

  // Handle different commands
  if (text.startsWith('/start')) {
    return handleStartCommand(username);
  } else if (text.startsWith('/help')) {
    return handleHelpCommand();
  } else if (text.startsWith('/latest')) {
    return await handleLatestCommand(text);
  } else if (text.startsWith('/search')) {
    return await handleSearchCommand(text);
  } else {
    return handleUnknownCommand();
  }
}

/**
 * Handle /start command
 */
function handleStartCommand(username: string): BotResponse {
  return {
    text: `🤖 <b>Welcome to Binance Token Scraper Bot, ${username}!</b>

I help you track the latest cryptocurrency tokens discovered from Binance sources.

<b>Available Commands:</b>
/latest [number] - Get latest tokens (max 50)
/search &lt;query&gt; - Search tokens by ticker or source
/help - Show this help message

<b>Examples:</b>
• <code>/latest 5</code> - Get 5 most recent tokens
• <code>/search BTC</code> - Search for BTC-related tokens
• <code>/search Telegram</code> - Search tokens from Telegram source

Let's get started! 🚀`,
    parse_mode: 'HTML',
    disable_web_page_preview: true
  };
}

/**
 * Handle /help command
 */
function handleHelpCommand(): BotResponse {
  return {
    text: `🔍 <b>Binance Token Scraper Bot Help</b>

<b>Commands:</b>

🆕 <code>/latest [number]</code>
Get the most recently discovered tokens
• Default: 10 tokens
• Maximum: 50 tokens
• Example: <code>/latest 5</code>

🔎 <code>/search &lt;query&gt;</code>
Search for tokens by ticker or source
• Searches ticker names and source platforms
• Example: <code>/search BTC</code>
• Example: <code>/search Telegram</code>

❓ <code>/help</code>
Show this help message

ℹ️ <b>About:</b>
This bot automatically discovers new cryptocurrency tokens by monitoring:
• Binance official Telegram channel
• BinanceWallet Twitter account  
• Binance announcement pages

All data is updated hourly via automated scraping.`,
    parse_mode: 'HTML',
    disable_web_page_preview: true
  };
}

/**
 * Handle /latest command
 */
async function handleLatestCommand(text: string): Promise<BotResponse> {
  try {
    // Parse the number parameter
    const parts = text.split(' ');
    let limit = 10; // default

    if (parts.length > 1) {
      const parsedLimit = parseInt(parts[1]);
      if (!isNaN(parsedLimit)) {
        limit = Math.min(Math.max(1, parsedLimit), 50); // Between 1 and 50
      }
    }

    const tokens = await getLatestTokens(limit);

    if (tokens.length === 0) {
      return {
        text: '📭 <b>No tokens found</b>\n\nNo tokens have been discovered yet. The scraper runs hourly to find new listings.',
        parse_mode: 'HTML'
      };
    }

    let response = `📈 <b>Latest ${tokens.length} Token${tokens.length > 1 ? 's' : ''}</b>\n\n`;

    tokens.forEach((token, index) => {
      const sourceEmoji = getSourceEmoji(token.source);
      const timeAgo = getTimeAgo(new Date(token.timestamp));
      const cgLink = `https://www.coingecko.com/en/coins/${token.ticker.toLowerCase()}`;
      
      response += `${index + 1}. <b>${token.ticker}</b> ${sourceEmoji}\n`;
      response += `   Source: ${token.source}\n`;
      response += `   Time: ${timeAgo}\n`;
      response += `   <a href="${cgLink}">View on CoinGecko</a>\n\n`;
    });

    response += '<i>💡 Use /search &lt;query&gt; to find specific tokens</i>';

    return {
      text: response,
      parse_mode: 'HTML',
      disable_web_page_preview: false
    };
  } catch (error) {
    logger.error('Error handling /latest command', error instanceof Error ? error : new Error(String(error)));
    return {
      text: '❌ <b>Error retrieving tokens</b>\n\nSorry, there was an error fetching the latest tokens. Please try again later.',
      parse_mode: 'HTML'
    };
  }
}

/**
 * Handle /search command
 */
async function handleSearchCommand(text: string): Promise<BotResponse> {
  try {
    // Extract search query
    const query = text.substring(7).trim(); // Remove "/search "
    
    if (!query) {
      return {
        text: '🔍 <b>Search Query Required</b>\n\nPlease provide a search term:\n\n<b>Examples:</b>\n• <code>/search BTC</code>\n• <code>/search Telegram</code>\n• <code>/search USDT</code>',
        parse_mode: 'HTML'
      };
    }

    const tokens = await searchTokens(query);

    if (tokens.length === 0) {
      return {
        text: `🔍 <b>No Results Found</b>\n\nNo tokens found matching "<code>${query}</code>"\n\n<b>Tips:</b>\n• Try searching by ticker symbol (e.g., BTC)\n• Search by source (e.g., Telegram, Twitter)\n• Check spelling and try shorter terms`,
        parse_mode: 'HTML'
      };
    }

    let response = `🔍 <b>Search Results for "${query}"</b>\n<i>Found ${tokens.length} token${tokens.length > 1 ? 's' : ''}</i>\n\n`;

    tokens.slice(0, 15).forEach((token, index) => { // Limit to 15 results to avoid message limits
      const sourceEmoji = getSourceEmoji(token.source);
      const timeAgo = getTimeAgo(new Date(token.timestamp));
      const cgLink = `https://www.coingecko.com/en/coins/${token.ticker.toLowerCase()}`;
      
      response += `${index + 1}. <b>${token.ticker}</b> ${sourceEmoji}\n`;
      response += `   Source: ${token.source}\n`;
      response += `   Time: ${timeAgo}\n`;
      response += `   <a href="${cgLink}">View on CoinGecko</a>\n\n`;
    });

    if (tokens.length > 15) {
      response += `<i>... and ${tokens.length - 15} more results</i>\n\n`;
    }

    response += '<i>💡 Use /latest to see newest tokens</i>';

    return {
      text: response,
      parse_mode: 'HTML',
      disable_web_page_preview: false
    };
  } catch (error) {
    logger.error('Error handling /search command', error instanceof Error ? error : new Error(String(error)));
    return {
      text: '❌ <b>Search Error</b>\n\nSorry, there was an error searching for tokens. Please try again later.',
      parse_mode: 'HTML'
    };
  }
}

/**
 * Handle unknown commands
 */
function handleUnknownCommand(): BotResponse {
  return {
    text: `❓ <b>Unknown Command</b>

I don't recognize that command. Here's what I can do:

<b>Available Commands:</b>
/latest [number] - Get latest tokens
/search &lt;query&gt; - Search tokens
/help - Show help message

<b>Examples:</b>
• <code>/latest 5</code>
• <code>/search BTC</code>

Type /help for more details! 🤖`,
    parse_mode: 'HTML',
    disable_web_page_preview: true
  };
}

/**
 * Get emoji for different sources
 */
function getSourceEmoji(source: string): string {
  const lowerSource = source.toLowerCase();
  if (lowerSource.includes('telegram')) return '💬';
  if (lowerSource.includes('twitter')) return '🐦';
  if (lowerSource.includes('binance')) return '🟡';
  return '📢';
}

/**
 * Get human-readable time ago string
 */
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) {
    return 'Just now';
  } else if (diffMins < 60) {
    return `${diffMins} min${diffMins > 1 ? 's' : ''} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Send a response to a Telegram chat
 */
export async function sendTelegramMessage(
  chatId: number,
  response: BotResponse,
  botToken: string
): Promise<boolean> {
  try {
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    
    const payload = {
      chat_id: chatId,
      text: response.text,
      parse_mode: response.parse_mode || 'HTML',
      disable_web_page_preview: response.disable_web_page_preview !== false
    };

    logger.debug('Sending Telegram bot response', {
      chatId,
      textLength: response.text.length,
      parseMode: response.parse_mode
    });

    const telegramResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await telegramResponse.json();

    if (result.ok) {
      logger.info('Successfully sent Telegram bot response', {
        chatId,
        messageId: result.result?.message_id
      });
      return true;
    } else {
      logger.error('Error sending Telegram bot response', new Error(result.description), {
        chatId,
        errorCode: result.error_code,
        description: result.description
      });
      return false;
    }
  } catch (error) {
    logger.error('Error in sendTelegramMessage', error instanceof Error ? error : new Error(String(error)), {
      chatId
    });
    return false;
  }
}