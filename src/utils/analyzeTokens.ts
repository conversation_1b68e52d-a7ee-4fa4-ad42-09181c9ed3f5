import { openrouter } from '@openrouter/ai-sdk-provider';
import { generateText } from 'ai';
import { withRateLimit } from '@/lib/rateLimitUtils';
import { Token, TelegramMessage, TwitterTweet, BinanceAnnouncement } from '@/lib/types';
import { getTokens, saveToken } from '@/lib/supabaseService';
import logger from '@/lib/logger';

// Common false positives that should be filtered out
const COMMON_FALSE_POSITIVES = [
  '888', // Likely a number, not a token
  'BTW', // Common abbreviation for "by the way"
  'APP', // Common abbreviation for "application"
  'USD', // US Dollar, not a crypto token
  'USD1', // Variant of USD
  'TGE', // Token Generation Event
  'APR', // Annual Percentage Rate
  'BNB', // Binance Coin (already well-known)
  'USDC', // USD Coin (already well-known)
  'FDUSD', // First Digital USD (already well-known)
  'API', // Application Programming Interface
  'URL', // Uniform Resource Locator
  'HTTP', // HyperText Transfer Protocol
  'HTTPS', // HyperText Transfer Protocol Secure
  'JSON', // JavaScript Object Notation
  'HTML', // HyperText Markup Language
  'CSS', // Cascading Style Sheets
  'SQL', // Structured Query Language
  'PDF', // Portable Document Format
  'PNG', // Portable Network Graphics
  'JPG', // Joint Photographic Experts Group
  'JPEG', // Joint Photographic Experts Group
  'GIF', // Graphics Interchange Format
  'SVG', // Scalable Vector Graphics
  'XML', // eXtensible Markup Language
  'CSV', // Comma-Separated Values
  'ZIP', // ZIP archive format
  'RAR', // RAR archive format
  'TAR', // Tape Archive
  'GZ', // GNU Zip
];

// Enhanced regex patterns for better token detection
const TOKEN_PATTERNS = {
  // Pattern for explicit token mentions
  explicit: /(?:token|coin|currency|ticker|symbol)[\s:]+([A-Z0-9]{3,6})\b/gi,
  // Pattern for listing announcements
  listing: /(?:listing|trading|launch|debut|introduce|add)[\s\w]*?([A-Z0-9]{3,6})\b/gi,
  // Pattern for trading pairs
  tradingPair: /([A-Z0-9]{3,6})\/(?:USDT|BTC|ETH|BNB|USD)\b/gi,
  // Pattern for price mentions
  price: /([A-Z0-9]{3,6})\s*(?:price|trading|at|\$)/gi,
  // Pattern for parenthetical mentions
  parenthetical: /\(([A-Z0-9]{3,6})\)/g,
  // Pattern for hashtags
  hashtag: /#([A-Z0-9]{3,6})\b/gi,
  // General pattern (fallback)
  general: /\b([A-Z0-9]{3,6})\b/g
};

// Get the list of known false positives from database
async function getFalsePositives(): Promise<string[]> {
  try {
    // Get tokens marked as false positives from database
    const tokens = await getTokens();
    const falsePositiveTokens = tokens
      .filter(token => token.falsePositive)
      .map(token => token.ticker);
    
    // Combine with common false positives
    const allFalsePositives = [...new Set([...COMMON_FALSE_POSITIVES, ...falsePositiveTokens])];
    
    logger.debug('Loaded false positives', { count: allFalsePositives.length });
    return allFalsePositives;
  } catch (err) {
    logger.error('Error reading false positives from database', err instanceof Error ? err : new Error(String(err)), {
      fallback: 'Using common false positives list'
    });
    return COMMON_FALSE_POSITIVES;
  }
}

// Validate if a string is a valid token ticker (3-6 uppercase alphanumeric characters)
// with additional filtering for common false positives
const isValidTicker = async (ticker: string): Promise<boolean> => {
  // Basic format check: 3-6 uppercase alphanumeric characters
  if (!/^[A-Z0-9]{3,6}$/.test(ticker)) {
    logger.debug('Invalid ticker format', { ticker, reason: 'format' });
    return false;
  }

  // Filter out strings that are all numbers
  if (/^\d+$/.test(ticker)) {
    logger.debug('Invalid ticker (all numbers)', { ticker, reason: 'all_numbers' });
    return false;
  }

  // Check if the ticker is in the false positives list
  const falsePositives = await getFalsePositives();
  if (falsePositives.includes(ticker)) {
    logger.debug('Invalid ticker (false positive)', { ticker, reason: 'false_positive' });
    return false;
  }

  logger.debug('Valid ticker', { ticker });
  return true;
};

/**
 * Enhanced regex-based token extraction with multiple patterns
 */
function extractTokensWithRegex(text: string): string[] {
  const foundTokens = new Set<string>();

  // Try each pattern in order of specificity
  const patterns = [
    TOKEN_PATTERNS.explicit,
    TOKEN_PATTERNS.listing,
    TOKEN_PATTERNS.tradingPair,
    TOKEN_PATTERNS.price,
    TOKEN_PATTERNS.parenthetical,
    TOKEN_PATTERNS.hashtag
  ];

  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      if (match[1]) {
        foundTokens.add(match[1].toUpperCase());
      }
    }
    // Reset regex lastIndex for next iteration
    pattern.lastIndex = 0;
  }

  // If no specific patterns found tokens, try general pattern
  if (foundTokens.size === 0) {
    logger.info('No tokens found with specific patterns, trying general pattern');
    let match;
    while ((match = TOKEN_PATTERNS.general.exec(text)) !== null) {
      if (match[1]) {
        foundTokens.add(match[1].toUpperCase());
      }
    }
    TOKEN_PATTERNS.general.lastIndex = 0;
  }

  const tokens = Array.from(foundTokens);
  logger.info('Regex extraction completed', {
    tokensFound: tokens.length,
    tokens: tokens.slice(0, 10).join(', ') + (tokens.length > 10 ? '...' : '')
  });

  return tokens;
};

// Find new tokens in the scraped text
export async function findNewTickers(
  allText: string,
  source: string,
  context?: {
    telegram?: TelegramMessage[],
    twitter?: TwitterTweet[],
    announcements?: BinanceAnnouncement[]
  }
): Promise<Token[]> {
  try {
    logger.info('Analyzing text for new tokens...', {
      textLength: allText.length,
      source,
      contextSources: {
        telegram: context?.telegram?.length || 0,
        twitter: context?.twitter?.length || 0,
        announcements: context?.announcements?.length || 0
      }
    });

    // Read the known tokens from the database
    let knownTokens: Token[] = [];

    try {
      knownTokens = await getTokens();
      logger.info('Loaded known tokens from database', { count: knownTokens.length });
    } catch (err) {
      logger.error('Error loading tokens from database', err instanceof Error ? err : new Error(String(err)));
      knownTokens = [];
    }

    // Call the AI to extract new tokens
    try {
      logger.info('Starting token extraction process');

      let tokens: string[] = [];

      // Check if OpenRouter API key is available
      if (!process.env.OPENROUTER_API_KEY) {
        logger.warn('OpenRouter API key is not set. Using enhanced regex fallback for token extraction.', null, {
          fallback: 'enhanced_regex',
          reason: 'missing_api_key'
        });

        tokens = extractTokensWithRegex(allText);
      } else {
        try {
          // Initialize OpenRouter client
          logger.info('Initializing OpenRouter client for token extraction', {
            model: 'google/gemini-2.5-flash-preview-05-20'
          });

          const ai = openrouter('google/gemini-2.5-flash-preview-05-20');

          // Create a list of known tickers to exclude
          const knownTickers = knownTokens.map(token => token.ticker);

          logger.info('Calling AI to extract tokens', {
            knownTokenCount: knownTickers.length,
            textLength: allText.length
          });

          const { text } = await withRateLimit('openrouter-api', async () => {
            return await generateText({
              model: ai,
              prompt: `You are a cryptocurrency token analyst. Analyze the following text and extract ONLY the new cryptocurrency token tickers that are being discussed.

IMPORTANT RULES:
1. Return ONLY a JSON array of uppercase token tickers, nothing else
2. Token tickers should be 3-6 characters long
3. Only include NEW tokens that appear to be cryptocurrency tokens
4. Do NOT include these known tokens: ${knownTickers.join(', ')}
5. Do NOT include common words, abbreviations, or non-crypto terms
6. Do NOT include explanations, just the JSON array
7. If no new tokens are found, return an empty array: []

Known tokens to EXCLUDE: ${knownTickers.join(', ')}

Text to analyze:
${allText}

Response format: ["TOKEN1", "TOKEN2", "TOKEN3"]`,
              temperature: 0.1,
              maxTokens: 200,
            });
          });

          logger.debug('Received AI response for token extraction', {
            responseLength: text.length,
            responsePreview: text.substring(0, 200)
          });

          // Parse the AI response
          let extractedTokens: unknown;
          try {
            // Try to parse as JSON first
            extractedTokens = JSON.parse(text.trim());
          } catch (parseError) {
            logger.debug('AI response is not valid JSON, trying to extract JSON from text', {
              parseError: parseError instanceof Error ? parseError.message : String(parseError)
            });
            
            // Try to find JSON array in the response
            const jsonMatch = text.match(/\[[\s\S]*?\]/);
            if (jsonMatch) {
              try {
                extractedTokens = JSON.parse(jsonMatch[0]);
                logger.debug('Successfully extracted JSON from AI response');
              } catch (secondParseError) {
                logger.debug('Could not parse extracted JSON', {
                  extractedText: jsonMatch[0],
                  parseError: secondParseError instanceof Error ? secondParseError.message : String(secondParseError)
                });
                extractedTokens = null;
              }
            } else {
              extractedTokens = null;
            }
          }

          if (extractedTokens !== null) {
            // Check if the extracted tokens is an array
            if (Array.isArray(extractedTokens)) {
              logger.info('Successfully extracted tokens array', {
                count: extractedTokens.length,
                tokens: extractedTokens.join(', ')
              });
              tokens = extractedTokens;
            } else if (typeof extractedTokens === 'object' && extractedTokens !== null) {
              // If it's an object but not an array, it might be a structured response
              logger.debug('Response is an object but not an array, checking for content');
              // Try to extract tokens from common response formats
              const responseObject = extractedTokens as Record<string, unknown>;
              if (Array.isArray(responseObject.tokens)) {
                logger.debug('Found tokens array in response object', {
                  property: 'tokens',
                  count: responseObject.tokens.length
                });
                tokens = responseObject.tokens as string[];
              } else if (Array.isArray(responseObject.tickers)) {
                logger.debug('Found tickers array in response object', {
                  property: 'tickers',
                  count: responseObject.tickers.length
                });
                tokens = responseObject.tickers as string[];
              } else if (Array.isArray(responseObject.results)) {
                logger.debug('Found results array in response object', {
                  property: 'results',
                  count: responseObject.results.length
                });
                tokens = responseObject.results as string[];
              } else {
                logger.warn('Could not find tokens array in response object', null, {
                  objectKeys: Object.keys(extractedTokens),
                  fallback: 'Using empty array'
                });
                tokens = [];
              }
            } else if (typeof extractedTokens === 'string') {
              // If it's a string, try to extract tokens using regex
              logger.debug('Extracting tokens from string response', {
                textLength: extractedTokens.length,
                textPreview: extractedTokens.substring(0, 100) + (extractedTokens.length > 100 ? '...' : '')
              });

              const tokenRegex = /\["([A-Z0-9]{3,6})"(?:,\s*"([A-Z0-9]{3,6})")*\]/g;
              const match = tokenRegex.exec(extractedTokens);
              if (match) {
                // Extract all capturing groups
                tokens = match.slice(1).filter(Boolean);
                logger.info('Extracted tokens from string', {
                  count: tokens.length,
                  tokens: tokens.join(', ')
                });
              } else {
                logger.warn('No tokens found in string response', null, {
                  fallback: 'Using empty array'
                });
                tokens = [];
              }
            } else {
              logger.warn('AI response is not an array, object, or string', null, {
                responseType: typeof extractedTokens,
                fallback: 'Using empty array'
              });
              tokens = [];
            }
          } else {
            logger.error('Error processing AI response', new Error('Could not parse AI response'), {
              fallback: 'Using enhanced regex extraction'
            });

            // Fallback to enhanced regex extraction
            tokens = extractTokensWithRegex(allText);
          }
        } catch (aiError) {
          logger.error('Error calling AI for token extraction', aiError instanceof Error ? aiError : new Error(String(aiError)), {
            fallback: 'Using enhanced regex extraction'
          });

          // Fallback to enhanced regex extraction
          tokens = extractTokensWithRegex(allText);
        }
      }

      // Filter out invalid tokens and tokens that are already known using parallel processing
      logger.info('Validating tokens in parallel', { count: tokens.length });

      // Process tokens in parallel batches to improve performance
      const batchSize = 10;
      const validNewTickerStrings: string[] = [];

      for (let i = 0; i < tokens.length; i += batchSize) {
        const batch = tokens.slice(i, i + batchSize);

        // Process batch in parallel
        const batchResults = await Promise.allSettled(
          batch.map(async (ticker) => {
            try {
              // Check if it's a valid ticker
              const isValid = await isValidTicker(ticker);
              if (!isValid) {
                logger.debug('Invalid ticker', { ticker });
                return null;
              }

              // Check if it's already in the known tokens
              const isKnown = knownTokens.some(token => token.ticker === ticker);
              if (isKnown) {
                logger.debug('Token already known', { ticker });
                return null;
              }

              logger.debug('Valid new token found', { ticker });
              return ticker;
            } catch (error) {
              logger.error('Error validating ticker', error instanceof Error ? error : new Error(String(error)), { ticker });
              return null;
            }
          })
        );

        // Collect valid results from this batch
        for (const result of batchResults) {
          if (result.status === 'fulfilled' && result.value) {
            validNewTickerStrings.push(result.value);
          }
        }
      }

      logger.info('Found new valid tokens', {
        count: validNewTickerStrings.length,
        tokens: validNewTickerStrings.join(', ')
      });

      // Create token objects with metadata
      const timestamp = new Date().toISOString();

      // Extract source URLs from the context if available
      const sourceUrls: Record<string, string> = {};

      logger.info('Extracting source URLs for tokens', { tokenCount: validNewTickerStrings.length });

      if (context && validNewTickerStrings.length > 0) {
        // Search for URLs in the context for each token
        for (const ticker of validNewTickerStrings) {
          // Check Telegram messages
          if (context.telegram) {
            for (const message of context.telegram) {
              if (message.text.includes(ticker) && (message.url || message.link)) {
                sourceUrls[ticker] = message.url || message.link || '';
                break;
              }
            }
          }

          // Check Twitter tweets
          if (!sourceUrls[ticker] && context.twitter) {
            for (const tweet of context.twitter) {
              if (tweet.text.includes(ticker) && (tweet.url || tweet.link)) {
                sourceUrls[ticker] = tweet.url || tweet.link || '';
                break;
              }
            }
          }

          // Check Binance announcements
          if (!sourceUrls[ticker] && context.announcements) {
            for (const announcement of context.announcements) {
              if (announcement.title.includes(ticker) && (announcement.url || announcement.link)) {
                sourceUrls[ticker] = announcement.url || announcement.link || '';
                break;
              }
            }
          }
        }
      }

      // If no URLs found in context, try to extract from the text itself
      if (Object.keys(sourceUrls).length === 0) {
        logger.info('No source URLs found in context, trying to extract from text');
        
        // Try to find URLs in the text
        const urlRegex = /https?:\/\/[^\s]+/g;
        const urls = allText.match(urlRegex) || [];
        
        if (urls.length > 0 && validNewTickerStrings.length > 0) {
          // Use the first URL found as a fallback
          const firstUrl = urls[0];
          if (firstUrl) {
            sourceUrls[validNewTickerStrings[0]] = firstUrl;
          }
        }
      }

      logger.info('Source URL extraction completed', {
        count: Object.keys(sourceUrls).length,
        coverage: `${Object.keys(sourceUrls).length}/${validNewTickerStrings.length}`
      });

      const validNewTokens: Token[] = validNewTickerStrings.map(ticker => ({
        ticker,
        source,
        timestamp,
        verified: false,
        falsePositive: false,
        sourceUrl: sourceUrls[ticker] || undefined
      }));

      // Save new tokens to database
      if (validNewTokens.length > 0) {
        logger.info('Saving new tokens to database', { count: validNewTokens.length });
        for (const token of validNewTokens) {
          try {
            await saveToken(token);
            logger.debug('Successfully saved token to database', { ticker: token.ticker });
          } catch (error) {
            logger.error('Error saving token to database', error instanceof Error ? error : new Error(String(error)), {
              ticker: token.ticker
            });
          }
        }
        logger.info('Finished saving new tokens to database', { count: validNewTokens.length });
      } else {
        logger.info('No new tokens to save to database');
      }

      return validNewTokens;
    } catch (err) {
      logger.error('Error in token extraction process', err instanceof Error ? err : new Error(String(err)));
      return [];
    }
  } catch (err) {
    logger.error('Error in findNewTickers', err instanceof Error ? err : new Error(String(err)));
    return [];
  }
}
