import { TelegramMessage } from '@/lib/types';
import logger from '@/lib/logger';
import { withRetry, withCircuitBreaker } from '@/lib/retryUtils';
import { withCache } from '@/lib/cacheUtils';
import { withRateLimit } from '@/lib/rateLimitUtils';

// Initialize Telegram client
const initTelegramClient = async () => {
  logger.info('Initializing Telegram client...');

  const apiId = parseInt(process.env.TG_API_ID || '0', 10);
  const apiHash = process.env.TG_API_HASH || '';
  const sessionString = process.env.TG_SESSION_STRING || '';

  if (!apiId || !apiHash || !sessionString) {
    logger.warn('Missing Telegram credentials', null, {
      missingEnvVars: ['TG_API_ID', 'TG_API_HASH', 'TG_SESSION_STRING'].filter(v => !process.env[v]),
      action: 'Please set TG_API_ID, TG_API_HASH, and TG_SESSION_STRING in your .env file'
    });
    return null;
  }

  try {
    // Import Telegram modules
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { TelegramClient } = require('telegram');
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { StringSession } = require('telegram/sessions');

    const stringSession = new StringSession(sessionString);

    // Create client with retry mechanism built in
    const client = new TelegramClient(stringSession, apiId, apiHash, {
      connectionRetries: 5, // Increased from 3
      retryDelay: 1000, // Start with 1 second delay
      maxAutoReconnect: 10, // Maximum number of auto reconnect attempts
      useWSS: true, // Use secure WebSocket
      floodSleepThreshold: 60, // Threshold in seconds for flood wait
    });

    // Connect with retry logic
    await withRetry(
      async () => {
        logger.debug('Attempting to connect to Telegram');
        await client.connect();
        return true;
      },
      {
        maxRetries: 3,
        initialDelayMs: 1000,
        maxDelayMs: 5000,
        isRetryable: (error) => {
          // Only retry network-related errors
          const errorStr = String(error).toLowerCase();
          const isNetworkError =
            errorStr.includes('network') ||
            errorStr.includes('connection') ||
            errorStr.includes('timeout') ||
            errorStr.includes('unreachable');

          logger.debug('Checking if error is retryable', {
            isNetworkError,
            errorMessage: errorStr.substring(0, 100)
          });

          return isNetworkError;
        }
      }
    );

    logger.info('Telegram client connected successfully');
    return client;
  } catch (error) {
    logger.error('Error initializing Telegram client', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
};

// Internal function to get Telegram posts without caching
async function _getTelegramPosts(): Promise<TelegramMessage[]> {
  return withCircuitBreaker(
    'telegram-api',
    async () => {
      try {
        logger.info('Fetching Telegram posts...');
        const client = await initTelegramClient();

        if (!client) {
          logger.warn('Telegram client not available', null, {
            reason: 'initialization_failed',
            action: 'Skipping Telegram posts'
          });
          return [];
        }

        const channelUsername = process.env.BINANCE_CHANNEL || 'Binance_Announcements';
        logger.info('Fetching messages from channel', { channelUsername });

        try {
          // Use retry logic for fetching messages
          const messages = await withRetry(
            async () => {
              logger.debug('Attempting to fetch messages from Telegram channel', { channelUsername });
              return await client.getMessages(channelUsername, {
                limit: 5,
              });
            },
            {
              maxRetries: 3,
              initialDelayMs: 1000,
              maxDelayMs: 5000,
              isRetryable: (error) => {
                // Only retry certain types of errors
                const errorStr = String(error).toLowerCase();
                const isRetryable =
                  errorStr.includes('network') ||
                  errorStr.includes('connection') ||
                  errorStr.includes('timeout') ||
                  errorStr.includes('unreachable') ||
                  errorStr.includes('flood') ||
                  errorStr.includes('too many requests');

                logger.debug('Checking if Telegram error is retryable', {
                  isRetryable,
                  errorMessage: errorStr.substring(0, 100)
                });

                return isRetryable;
              }
            }
          );

          // Always disconnect the client when done
          try {
            await client.disconnect();
            logger.debug('Telegram client disconnected successfully');
          } catch (disconnectError) {
            logger.warn('Error disconnecting Telegram client', null, {
              error: String(disconnectError)
            });
          }

          logger.info('Retrieved Telegram messages', { count: messages.length });

          // Create a map to deduplicate messages by ID
          const messageMap = new Map();

          messages.forEach((msg: { id: number; message?: string; date: number }) => {
            if (!messageMap.has(msg.id)) {
              messageMap.set(msg.id, {
                id: msg.id,
                text: msg.message || '',
                date: new Date(msg.date * 1000), // Convert Unix timestamp to Date
                url: `https://t.me/${channelUsername}/${msg.id}`,
              });
            }
          });

          // Convert map to array
          const result = Array.from(messageMap.values());
          logger.debug('Processed Telegram messages', {
            rawCount: messages.length,
            uniqueCount: result.length
          });

          return result;
        } catch (channelError) {
          logger.error('Error fetching messages from channel',
            channelError instanceof Error ? channelError : new Error(String(channelError)),
            { channelUsername }
          );

          // Always disconnect the client on error
          try {
            await client.disconnect();
            logger.debug('Telegram client disconnected after error');
          } catch (disconnectError) {
            logger.warn('Error disconnecting Telegram client after error', null, {
              error: String(disconnectError)
            });
          }

          return [];
        }
      } catch (error) {
        logger.error('Error in Telegram posts fetching',
          error instanceof Error ? error : new Error(String(error))
        );
        return [];
      }
    },
    {
      failureThreshold: 3,
      resetTimeoutMs: 60000, // 1 minute
      successThreshold: 2,
      isFailure: (error) => {
        // Determine if this error should trigger the circuit breaker
        const errorStr = String(error).toLowerCase();

        // Don't count configuration errors as circuit breaker failures
        const isConfigError =
          errorStr.includes('credentials') ||
          errorStr.includes('api_id') ||
          errorStr.includes('api_hash') ||
          errorStr.includes('session');

        return !isConfigError;
      }
    }
  );
}

// Cached version of getTelegramPosts with 5-minute TTL
export const getTelegramPosts = withCache(_getTelegramPosts, {
  ttl: 5 * 60 * 1000, // 5 minutes
  staleWhileRevalidate: true
});
