import { Token } from '@/lib/types';
import logger from '@/lib/logger';

/**
 * Sends a notification to a Telegram bot about newly discovered tokens
 * @param tokens Array of newly discovered tokens
 * @returns Promise that resolves to true if the notification was sent successfully
 */
export async function sendTokenNotification(tokens: Token[]): Promise<boolean> {
  try {
    if (tokens.length === 0) {
      logger.info('No new tokens to notify about');
      return true;
    }

    const botToken = process.env.NOTIFICATION_BOT_TOKEN;
    const chatId = process.env.NOTIFICATION_CHAT_ID;

    if (!botToken || !chatId) {
      logger.warn('Missing Telegram notification credentials', null, {
        missingEnvVars: ['NOTIFICATION_BOT_TOKEN', 'NOTIFICATION_CHAT_ID'].filter(v => !process.env[v]),
        action: 'Please set NOTIFICATION_BOT_TOKEN and NOTIFICATION_CHAT_ID in your .env file'
      });
      return false;
    }

    logger.info('Sending notification to Telegram', { tokenCount: tokens.length });

    // Create a formatted message
    const message = formatTokenMessage(tokens);
    logger.debug('Formatted notification message', { messageLength: message.length });

    // Send the message to Telegram
    logger.debug('Sending request to Telegram API');
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML',
        disable_web_page_preview: false,
      }),
    });

    const result = await response.json();

    if (result.ok) {
      logger.info('Telegram notification sent successfully', {
        messageId: result.result?.message_id,
        chatId
      });
      return true;
    } else {
      logger.error('Error sending Telegram notification', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return false;
    }
  } catch (error) {
    logger.error('Error in sendTokenNotification', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * Formats a message about newly discovered tokens
 * @param tokens Array of newly discovered tokens
 * @returns Formatted message string
 */
function formatTokenMessage(tokens: Token[]): string {
  const now = new Date().toLocaleString();

  let message = `🚨 <b>New Token Alert</b> 🚨\n\n`;
  message += `<b>${tokens.length} new token${tokens.length > 1 ? 's' : ''} discovered at ${now}</b>\n\n`;

  tokens.forEach((token, index) => {
    const sourceText = token.sourceUrl
      ? `<a href="${token.sourceUrl}">${token.source}</a>`
      : token.source;

    // Add CoinGecko link
    const cgLink = `https://www.coingecko.com/en/coins/${token.ticker.toLowerCase()}`;

    message += `${index + 1}. <b>${token.ticker}</b>\n`;
    message += `   Source: ${sourceText}\n`;
    message += `   CoinGecko: <a href="${cgLink}">View on CoinGecko</a>\n`;
    message += `   Time: ${new Date(token.timestamp).toLocaleString()}\n\n`;
  });

  message += `\n<i>Powered by Binance Token Scraper</i>`;

  return message;
}

/**
 * Sends a test notification to Telegram
 * @returns Promise that resolves to true if the test notification was sent successfully
 */
export async function sendTestNotification(): Promise<boolean> {
  try {
    const botToken = process.env.NOTIFICATION_BOT_TOKEN;
    const chatId = process.env.NOTIFICATION_CHAT_ID;

    if (!botToken || !chatId) {
      logger.warn('Missing Telegram notification credentials', null, {
        missingEnvVars: ['NOTIFICATION_BOT_TOKEN', 'NOTIFICATION_CHAT_ID'].filter(v => !process.env[v]),
        action: 'Please set NOTIFICATION_BOT_TOKEN and NOTIFICATION_CHAT_ID in your .env file'
      });
      return false;
    }

    logger.info('Sending test notification to Telegram');

    // Create a test token
    const testToken: Token = {
      ticker: 'TEST',
      source: 'Test Notification',
      timestamp: new Date().toISOString(),
      verified: false,
      falsePositive: false,
      sourceUrl: 'https://example.com/test',
    };

    // Create a formatted message
    const message = formatTokenMessage([testToken]);
    logger.debug('Formatted test notification message', { messageLength: message.length });

    // Send the message to Telegram
    logger.debug('Sending test request to Telegram API');
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML',
        disable_web_page_preview: false,
      }),
    });

    const result = await response.json();

    if (result.ok) {
      logger.info('Test notification sent successfully', {
        messageId: result.result?.message_id,
        chatId
      });
      return true;
    } else {
      logger.error('Error sending test notification', new Error(result.description), {
        errorCode: result.error_code,
        description: result.description
      });
      return false;
    }
  } catch (error) {
    logger.error('Error in sendTestNotification', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}
