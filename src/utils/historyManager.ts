import { TelegramMessage, TwitterTweet, BinanceAnnouncement } from '@/lib/types';
import { 
  getTelegramMessages, 
  getTwitterTweets, 
  getBinanceAnnouncements,
  saveTelegramMessages,
  saveTwitterTweets,
  saveBinanceAnnouncements
} from '@/lib/supabaseService';
import logger from '@/lib/logger';

// Get Telegram history from database
export async function getTelegramHistory(): Promise<TelegramMessage[]> {
  try {
    return await getTelegramMessages();
  } catch (error) {
    logger.error('Error reading Telegram history from database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

// Get Twitter history from database
export async function getTwitterHistory(): Promise<TwitterTweet[]> {
  try {
    return await getTwitterTweets();
  } catch (error) {
    logger.error('Error reading Twitter history from database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

// Get Binance history from database
export async function getBinanceHistory(): Promise<BinanceAnnouncement[]> {
  try {
    return await getBinanceAnnouncements();
  } catch (error) {
    logger.error('Error reading Binance history from database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

// Add new Telegram messages to database (with deduplication)
export async function addTelegramMessages(newMessages: TelegramMessage[]): Promise<TelegramMessage[]> {
  if (newMessages.length === 0) {
    logger.info('No new Telegram messages to process');
    return [];
  }

  try {
    const history = await getTelegramHistory();
    const existingIds = new Set(history.map(msg => msg.id));

    // Filter out duplicates
    const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg.id));

    if (uniqueNewMessages.length > 0) {
      logger.info(`Adding ${uniqueNewMessages.length} new Telegram messages to database`);
      await saveTelegramMessages(uniqueNewMessages);
    } else {
      logger.info('No new Telegram messages to add to database');
    }

    return uniqueNewMessages;
  } catch (error) {
    logger.error('Error adding Telegram messages to database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

// Add new Twitter tweets to database (with deduplication)
export async function addTwitterTweets(newTweets: TwitterTweet[]): Promise<TwitterTweet[]> {
  if (newTweets.length === 0) {
    logger.info('No new Twitter tweets to process');
    return [];
  }

  try {
    const history = await getTwitterHistory();
    const existingIds = new Set(history.map(tweet => tweet.id));

    // Filter out duplicates
    const uniqueNewTweets = newTweets.filter(tweet => !existingIds.has(tweet.id));

    if (uniqueNewTweets.length > 0) {
      logger.info(`Adding ${uniqueNewTweets.length} new Twitter tweets to database`);
      await saveTwitterTweets(uniqueNewTweets);
    } else {
      logger.info('No new Twitter tweets to add to database');
    }

    return uniqueNewTweets;
  } catch (error) {
    logger.error('Error adding Twitter tweets to database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

// Add new Binance announcements to database (with deduplication)
export async function addBinanceAnnouncements(newAnnouncements: BinanceAnnouncement[]): Promise<BinanceAnnouncement[]> {
  if (newAnnouncements.length === 0) {
    logger.info('No new Binance announcements to process');
    return [];
  }

  try {
    const history = await getBinanceHistory();
    const existingUrls = new Set(history.map(ann => ann.url));

    // Filter out duplicates based on URL (since IDs might be generated)
    const uniqueNewAnnouncements = newAnnouncements.filter(ann => !existingUrls.has(ann.url));

    if (uniqueNewAnnouncements.length > 0) {
      logger.info(`Adding ${uniqueNewAnnouncements.length} new Binance announcements to database`);
      await saveBinanceAnnouncements(uniqueNewAnnouncements);
    } else {
      logger.info('No new Binance announcements to add to database');
    }

    return uniqueNewAnnouncements;
  } catch (error) {
    logger.error('Error adding Binance announcements to database', error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}
