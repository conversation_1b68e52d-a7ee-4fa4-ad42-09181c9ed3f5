import Link from 'next/link';
import TokenList from '@/components/TokenList';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getTokens } from '@/lib/supabaseService';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RotateCw } from 'lucide-react';

export default async function TokensPage() {
  const allTokens = await getTokens();

  // Sort tokens by timestamp (newest first) and filter out false positives
  const tokens = allTokens
    .filter(token => !token.falsePositive)
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  console.log('[INFO]: Rendering tokens page', {
    totalTokens: allTokens.length,
    filteredTokens: tokens.length
  });

  return (
    <div className="min-h-screen p-6 pb-20 sm:p-8 max-w-7xl mx-auto">
      <Header tokenCount={tokens.length} />

      <main>
        {tokens.length > 0 ? (
          <TokenList tokens={tokens} />
        ) : (
          <Card className="text-center p-6">
            <CardHeader>
              <CardTitle>No Tokens Found</CardTitle>
              <CardDescription>
                The scraper hasn&apos;t discovered any tokens yet.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="success" size="lg" className="mt-4">
                <Link href="/api/scrape" className="flex items-center gap-2">
                  <RotateCw className="h-5 w-5" />
                  Run Scraper Now
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </main>

      <Footer />
    </div>
  );
}
