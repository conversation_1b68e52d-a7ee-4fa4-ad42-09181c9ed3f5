import { NextResponse } from 'next/server';
import { getTokens } from '@/lib/supabaseService';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createApiHandler } from '@/lib/errorUtils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const GET = createApiHandler(async (_req: Request) => {
  const requestId = getRequestId();
  logger.info('Refresh tokens API called', { requestId });

  try {
    // Read the current tokens from database
    logger.info('Reading tokens from database for refresh');

    const tokens = await getTokens();

    logger.info('Successfully fetched tokens from database', {
      count: tokens.length,
      requestId
    });

    // Return the tokens (they're already deduplicated in the database)
    return NextResponse.json({
      success: true,
      message: 'Tokens refreshed successfully',
      tokens,
      count: tokens.length,
      requestId,
    });
  } catch (error) {
    // The createApiHandler will handle this error and convert it to a proper response
    if (error instanceof AppError) {
      throw error;
    }

    // Convert other errors to AppError
    throw AppError.internal(
      'Error refreshing tokens',
      { service: 'supabase' },
      error instanceof Error ? error : new Error(String(error))
    );
  }
});
