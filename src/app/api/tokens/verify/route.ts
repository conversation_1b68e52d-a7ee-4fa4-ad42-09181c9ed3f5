import { NextRequest, NextResponse } from 'next/server';
import { updateToken } from '@/lib/supabaseService';

export async function POST(request: NextRequest) {
  console.log('Verify token API called');

  try {
    // Parse the request body
    const body = await request.json();
    const { ticker, verified = true } = body;

    console.log('Request body:', body);

    if (!ticker) {
      console.error('Missing ticker in request');
      return NextResponse.json(
        { success: false, message: 'Ticker is required' },
        { status: 400 }
      );
    }

    console.log(`Verifying token ${ticker} with status: ${verified}`);

    try {
      // Update the token in Supabase
      await updateToken(ticker, { verified });
    } catch (error) {
      console.error('Error updating token in database:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to update token in database' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Token ${ticker} ${verified ? 'verified' : 'unverified'} successfully`
    });
  } catch (error) {
    console.error('Error verifying token:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to verify token',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
