import { NextRequest, NextResponse } from 'next/server';
import { updateToken } from '@/lib/supabaseService';

export async function POST(request: NextRequest) {
  console.log('Mark false positive API called');

  try {
    // Parse the request body
    const body = await request.json();
    const { ticker } = body;

    console.log('Request body:', body);

    if (!ticker) {
      console.error('Missing ticker in request');
      return NextResponse.json(
        { success: false, message: 'Ticker is required' },
        { status: 400 }
      );
    }

    console.log(`Marking token ${ticker} as false positive`);

    try {
      // Update the token in Supabase
      await updateToken(ticker, { falsePositive: true });
    } catch (error) {
      console.error('Error updating token in database:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to update token in database' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Token ${ticker} marked as false positive`
    });
  } catch (error) {
    console.error('Error marking token as false positive:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to mark token as false positive',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
