import { NextResponse } from 'next/server';
import { getTelegramPosts } from '@/utils/telegram';
import { getTwitterPosts } from '@/utils/twitter';
import { getBinanceAnns } from '@/utils/binance';
import { findNewTickers } from '@/utils/analyzeTokens';
import { sendTokenNotification } from '@/utils/notifyTelegram';
import { ScrapeResult, Token } from '@/lib/types';
import {
  addTelegramMessages,
  addTwitterTweets,
  addBinanceAnnouncements
} from '@/utils/historyManager';
import { saveScrapeResult } from '@/lib/supabaseService';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createApiHandler } from '@/lib/errorUtils';
import { logMemoryUsage, streamProcess, createArrayStream, processTextChunks, MemorySafeStringBuilder } from '@/lib/memoryUtils';

// Use the createApiHandler to wrap the handler function with standardized error handling
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const GET = createApiHandler(async (_req: Request) => {
  const requestId = getRequestId();
  logger.info('Starting scrape operation...', { requestId });

  // Log initial memory usage
  logMemoryUsage('Initial memory usage');

  // Run all scraping operations in parallel
  logger.info('Running parallel scraping operations...', { sources: ['telegram', 'twitter', 'binance'] });
  const [scrapedTelegram, scrapedTwitter, scrapedAnnouncements] = await Promise.all([
    getTelegramPosts().catch(error => {
      logger.error('Error in Telegram scraping', error, { source: 'telegram' });
      // Don't throw, just return empty array to continue with other sources
      return [];
    }),
    getTwitterPosts().catch(error => {
      logger.error('Error in Twitter scraping', error, { source: 'twitter' });
      // Don't throw, just return empty array to continue with other sources
      return [];
    }),
    getBinanceAnns().catch(error => {
      logger.error('Error in Binance scraping', error, { source: 'binance' });
      // Don't throw, just return empty array to continue with other sources
      return [];
    }),
  ]);

  // Check if all scraping operations failed
  if (scrapedTelegram.length === 0 && scrapedTwitter.length === 0 && scrapedAnnouncements.length === 0) {
    logger.warn('All scraping operations failed or returned empty results', null, {
      requestId
    });
    // Continue anyway, but log a warning
  }

  logger.info('Scraping completed', {
    telegramCount: scrapedTelegram.length,
    twitterCount: scrapedTwitter.length,
    binanceCount: scrapedAnnouncements.length
  });

  // Log memory usage after scraping
  logMemoryUsage('Memory usage after scraping');

  // Add new items to history and get only unique new items
  logger.info('Checking for duplicates and adding to history...');
  const [telegram, twitter, announcements] = await Promise.all([
    addTelegramMessages(scrapedTelegram).catch(error => {
      throw AppError.internal(
        'Failed to process Telegram messages',
        { count: scrapedTelegram.length },
        error instanceof Error ? error : new Error(String(error))
      );
    }),
    addTwitterTweets(scrapedTwitter).catch(error => {
      throw AppError.internal(
        'Failed to process Twitter tweets',
        { count: scrapedTwitter.length },
        error instanceof Error ? error : new Error(String(error))
      );
    }),
    addBinanceAnnouncements(scrapedAnnouncements).catch(error => {
      throw AppError.internal(
        'Failed to process Binance announcements',
        { count: scrapedAnnouncements.length },
        error instanceof Error ? error : new Error(String(error))
      );
    }),
  ]);

  logger.info('Deduplication completed', {
    uniqueTelegramCount: telegram.length,
    uniqueTwitterCount: twitter.length,
    uniqueBinanceCount: announcements.length
  });

  // Use memory-efficient text processing
  logger.info('Preparing text for analysis with memory-safe processing');

  // Create arrays of text content
  const telegramTexts = telegram.map(m => m.text);
  const twitterTexts = twitter.map(t => t.text);
  const announcementTitles = announcements.map(a => a.title);

  // Combine all text chunks
  const textChunks = [
    ...telegramTexts,
    ...twitterTexts,
    ...announcementTitles
  ];

  // Use memory-safe string builder to combine text
  const stringBuilder = new MemorySafeStringBuilder(20); // 20MB limit

  for (const chunk of textChunks) {
    stringBuilder.append(chunk + '\n');
  }

  const scrapeText = stringBuilder.toString();

  if (scrapeText.length === 0) {
    logger.warn('No text content available for analysis', null, {
      requestId
    });
    // Continue anyway, but log a warning
  }

  logger.info('Combined text for analysis', {
    textLength: scrapeText.length,
    memorySizeMB: stringBuilder.getSizeMB().toFixed(2),
    chunkCount: stringBuilder.getChunkCount()
  });

  // Clear the string builder to free memory
  stringBuilder.clear();

  // Log memory usage after text processing
  logMemoryUsage('Memory usage after text processing');

  // Find new tokens
  logger.info('Starting token analysis', { textLength: scrapeText.length });

  let addedTokens: Token[] = [];
  try {
    // Pass the context with all sources to the token analysis
    const context = {
      telegram,
      twitter,
      announcements
    };

    addedTokens = await findNewTickers(scrapeText, 'auto', context);
    logger.info('Token analysis completed', {
      tokenCount: addedTokens.length,
      tokens: addedTokens.map(t => t.ticker).join(', ')
    });
  } catch (error) {
    // Log but don't throw, as we can still return the scraped data even if token analysis fails
    logger.error('Error in token analysis', error instanceof Error ? error : new Error(String(error)), {
      textLength: scrapeText.length
    });
  }

  // Log memory usage after token analysis
  logMemoryUsage('Memory usage after token analysis');

  // Create the payload
  const payload: ScrapeResult = {
    telegram,
    twitter,
    announcements,
    addedTokens,
    fetchedAt: new Date().toISOString(),
  };

  // Log memory usage before saving results
  logMemoryUsage('Memory usage before saving results');

  // Save the results to database
  try {
    await saveScrapeResult(payload);
    logger.info('Saved results to database');
  } catch (error) {
    // Log but don't throw, as we can still return the scraped data even if saving fails
    logger.error('Error saving scrape results', error instanceof Error ? error : new Error(String(error)));
  }

  // Send notification if new tokens were found
  let notificationSent = false;
  if (addedTokens.length > 0) {
    logger.info('Sending notification about new tokens...', { tokenCount: addedTokens.length });

    // Process tokens in batches if there are many
    if (addedTokens.length > 10) {
      logger.info('Processing tokens in batches for notification', { batchSize: 10 });

      // Create batches of tokens
      const tokenBatches: Token[][] = [];
      for (let i = 0; i < addedTokens.length; i += 10) {
        tokenBatches.push(addedTokens.slice(i, i + 10));
      }

      // Send notifications for each batch
      let allSuccessful = true;
      for (let i = 0; i < tokenBatches.length; i++) {
        const batch = tokenBatches[i];
        try {
          logger.info(`Sending notification for batch ${i + 1}/${tokenBatches.length}`, { tokenCount: batch.length });
          const batchSuccess = await sendTokenNotification(batch);
          if (!batchSuccess) {
            allSuccessful = false;
            logger.warn(`Notification for batch ${i + 1} failed`);
          }

          // Add a small delay between batches to avoid rate limiting
          if (i < tokenBatches.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          allSuccessful = false;
          logger.error(`Error sending notification for batch ${i + 1}`, error instanceof Error ? error : new Error(String(error)));
        }
      }

      notificationSent = allSuccessful;
    } else {
      // Send a single notification for a small number of tokens
      try {
        notificationSent = await sendTokenNotification(addedTokens);
        logger.info('Notification status', { success: notificationSent });
      } catch (error) {
        // Log but don't throw, as we can still return the scraped data even if notification fails
        logger.error('Error sending notification', error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  // Log final memory usage
  logMemoryUsage('Final memory usage');

  // Return the results
  logger.info('Scrape operation completed successfully', {
    telegramCount: telegram.length,
    twitterCount: twitter.length,
    announcementsCount: announcements.length,
    addedTokensCount: addedTokens.length,
    notificationSent,
  });

  return NextResponse.json({
    success: true,
    message: 'Scrape completed successfully',
    stats: {
      telegram: telegram.length,
      twitter: twitter.length,
      announcements: announcements.length,
      addedTokens: addedTokens.length,
      notificationSent,
    },
    requestId: getRequestId(),
  });
});
