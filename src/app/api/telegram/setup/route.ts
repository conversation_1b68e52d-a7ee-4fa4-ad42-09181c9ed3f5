import { NextResponse } from 'next/server';
import { 
  completeBotSetup, 
  validateBotConfiguration, 
  generateWebhookUrl,
  getWebhookInfo,
  getBotInfo
} from '@/lib/telegramBotSetup';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createA<PERSON>Handler } from '@/lib/errorUtils';

/**
 * Setup Telegram bot webhook and configuration
 * POST: Set up the bot with webhook
 * GET: Check current bot configuration status
 */
export const POST = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Telegram bot setup requested', { requestId });

  try {
    // Validate bot configuration
    const config = validateBotConfiguration();
    
    if (!config.isFullyConfigured) {
      logger.error('Bot configuration incomplete', null, {
        missingVars: config.missingVars
      });
      throw AppError.validation(
        'Bot configuration incomplete',
        { 
          missingVars: config.missingVars,
          message: `Please set the following environment variables: ${config.missingVars.join(', ')}`
        }
      );
    }

    const botToken = process.env.NOTIFICATION_BOT_TOKEN!;

    // Get webhook URL from request or generate from host
    let webhookUrl: string;
    try {
      const body = await req.json();
      webhookUrl = body.webhookUrl;
    } catch {
      // If no body, generate from request host
      const host = req.headers.get('host') || 'localhost:3000';
      const protocol = host.includes('localhost') ? 'http' : 'https';
      webhookUrl = generateWebhookUrl(`${protocol}://${host}`);
    }

    if (!webhookUrl) {
      throw AppError.validation(
        'Webhook URL required',
        { message: 'Please provide webhookUrl in request body or ensure proper host header' }
      );
    }

    logger.info('Setting up bot with webhook', { webhookUrl });

    // Perform complete bot setup
    const success = await completeBotSetup(botToken, webhookUrl);

    if (!success) {
      throw AppError.externalService(
        'Bot setup failed',
        { service: 'telegram', webhookUrl }
      );
    }

    // Get final webhook info to confirm
    const webhookInfo = await getWebhookInfo(botToken);

    logger.info('Bot setup completed successfully', {
      webhookUrl,
      webhookActive: !!webhookInfo?.url,
      requestId
    });

    return NextResponse.json({
      success: true,
      message: 'Bot setup completed successfully',
      webhookUrl,
      webhookInfo,
      requestId
    });

  } catch (error) {
    throw error;
  }
});

/**
 * Get current bot configuration and status
 */
export const GET = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Bot status check requested', { requestId });

  try {
    // Check configuration
    const config = validateBotConfiguration();
    
    let botInfo = null;
    let webhookInfo = null;
    let errors: string[] = [];

    if (config.hasToken) {
      const botToken = process.env.NOTIFICATION_BOT_TOKEN!;
      
      // Get bot info
      try {
        botInfo = await getBotInfo(botToken);
      } catch (error) {
        errors.push('Failed to get bot info - check bot token');
        logger.error('Failed to get bot info', error instanceof Error ? error : new Error(String(error)));
      }

      // Get webhook info
      try {
        webhookInfo = await getWebhookInfo(botToken);
      } catch (error) {
        errors.push('Failed to get webhook info');
        logger.error('Failed to get webhook info', error instanceof Error ? error : new Error(String(error)));
      }
    }

    // Generate suggested webhook URL
    const host = req.headers.get('host') || 'localhost:3000';
    const protocol = host.includes('localhost') ? 'http' : 'https';
    const suggestedWebhookUrl = generateWebhookUrl(`${protocol}://${host}`);

    const status = {
      configuration: config,
      botInfo,
      webhookInfo,
      suggestedWebhookUrl,
      errors,
      recommendations: [] as string[],
      requestId
    };

    // Add recommendations
    if (!config.hasToken) {
      status.recommendations.push('Set NOTIFICATION_BOT_TOKEN environment variable');
    }
    if (!config.hasChatId) {
      status.recommendations.push('Set NOTIFICATION_CHAT_ID environment variable for notifications');
    }
    if (webhookInfo && !webhookInfo.url) {
      status.recommendations.push('Set up webhook using POST /api/telegram/setup');
    }
    if (webhookInfo?.last_error_message) {
      status.recommendations.push(`Fix webhook error: ${webhookInfo.last_error_message}`);
    }

    logger.info('Bot status check completed', {
      hasToken: config.hasToken,
      hasChatId: config.hasChatId,
      hasWebhook: !!webhookInfo?.url,
      errorCount: errors.length,
      requestId
    });

    return NextResponse.json(status);

  } catch (error) {
    throw error;
  }
});