import { NextResponse } from 'next/server';
import { processCommand, sendTelegramMessage, TelegramUpdate } from '@/utils/telegramBotCommands';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createA<PERSON>Handler } from '@/lib/errorUtils';

/**
 * Handle incoming Telegram webhook updates
 * This endpoint processes bot commands and responds to users
 */
export const POST = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Telegram webhook called', { requestId });

  try {
    // Verify bot token from environment
    const botToken = process.env.NOTIFICATION_BOT_TOKEN;
    if (!botToken) {
      logger.error('Telegram bot token not configured', null, {
        missingEnvVar: 'NOTIFICATION_BOT_TOKEN'
      });
      throw AppError.validation(
        'Telegram bot not configured',
        { missingEnvVar: 'NOTIFICATION_BOT_TOKEN' }
      );
    }

    // Parse the webhook update
    let update: TelegramUpdate;
    try {
      const body = await req.text();
      logger.debug('Received Telegram webhook payload', {
        payloadLength: body.length,
        requestId
      });
      update = JSON.parse(body);
    } catch (parseError) {
      logger.error('Error parsing Telegram webhook payload', parseError instanceof Error ? parseError : new Error(String(parseError)));
      throw AppError.validation(
        'Invalid webhook payload',
        { error: 'Failed to parse JSON' }
      );
    }

    // Validate update structure
    if (!update || typeof update.update_id !== 'number') {
      logger.warn('Invalid Telegram update structure', null, { update });
      throw AppError.validation(
        'Invalid update structure',
        { error: 'Missing update_id' }
      );
    }

    logger.info('Processing Telegram update', {
      updateId: update.update_id,
      chatId: update.message?.chat?.id,
      userId: update.message?.from?.id,
      hasMessage: !!update.message,
      hasText: !!update.message?.text,
      requestId
    });

    // Skip if no message or text
    if (!update.message || !update.message.text) {
      logger.debug('Skipping update without text message', {
        updateId: update.update_id,
        requestId
      });
      return NextResponse.json({ ok: true, skipped: true });
    }

    // Process the command and get response
    const response = await processCommand(update);
    
    if (!response) {
      logger.debug('No response generated for command', {
        updateId: update.update_id,
        text: update.message.text,
        requestId
      });
      return NextResponse.json({ ok: true, no_response: true });
    }

    // Send response back to user
    const chatId = update.message.chat.id;
    const success = await sendTelegramMessage(chatId, response, botToken);

    if (!success) {
      logger.error('Failed to send Telegram response', null, {
        chatId,
        updateId: update.update_id,
        requestId
      });
      throw AppError.externalService(
        'Failed to send response to user',
        { service: 'telegram', chatId }
      );
    }

    logger.info('Successfully processed Telegram webhook', {
      updateId: update.update_id,
      chatId,
      responseLength: response.text.length,
      requestId
    });

    return NextResponse.json({
      ok: true,
      update_id: update.update_id,
      processed: true,
      requestId
    });

  } catch (error) {
    // The createApiHandler will handle error logging and response formatting
    throw error;
  }
});

/**
 * Handle GET requests for webhook verification
 * Some webhook services require GET endpoint verification
 */
export const GET = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Telegram webhook verification called', { requestId });

  // Check for webhook verification parameters
  const url = new URL(req.url);
  const challenge = url.searchParams.get('hub.challenge');
  
  if (challenge) {
    // Return challenge for webhook verification
    logger.info('Webhook verification successful', { challenge, requestId });
    return NextResponse.json({ 
      ok: true, 
      challenge,
      message: 'Webhook verification successful',
      requestId
    });
  }

  // Return webhook status
  const botToken = process.env.NOTIFICATION_BOT_TOKEN;
  const isConfigured = !!botToken;

  return NextResponse.json({
    ok: true,
    status: 'Telegram webhook endpoint active',
    configured: isConfigured,
    timestamp: new Date().toISOString(),
    requestId
  });
});