import { NextResponse } from 'next/server';
import { sendTestNotification } from '@/utils/notifyTelegram';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createApiHandler } from '@/lib/errorUtils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const GET = createApiHandler(async (_req: Request) => {
  const requestId = getRequestId();
  logger.info('Test notification API called', { requestId });

  logger.info('Sending test notification');

  // Send test notification with proper error handling
  try {
    const success = await sendTestNotification();

    if (!success) {
      // If the notification service returns false, throw a specific error
      throw AppError.externalService(
        'Failed to send test notification',
        { service: 'telegram' }
      );
    }

    logger.info('Test notification sent successfully');

    return NextResponse.json({
      success: true,
      message: 'Test notification sent successfully',
      requestId,
    });
  } catch (error) {
    // The createApiHandler will handle this error and convert it to a proper response
    if (error instanceof AppError) {
      throw error;
    }

    // Convert other errors to AppError
    throw AppError.externalService(
      'Error sending test notification',
      { service: 'telegram' },
      error instanceof Error ? error : new Error(String(error))
    );
  }
});
