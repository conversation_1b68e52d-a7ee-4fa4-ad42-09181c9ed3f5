import { NextResponse } from 'next/server';
import { sendTokenNotification } from '@/utils/notifyTelegram';
import { Token } from '@/lib/types';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createA<PERSON><PERSON>and<PERSON> } from '@/lib/errorUtils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const GET = createApiHandler(async (_req: Request) => {
  const requestId = getRequestId();
  logger.info('Notification API called', { requestId });

  // Create sample tokens for testing
  const sampleTokens: Token[] = [
    {
      ticker: 'SAMPLE',
      source: 'Telegram',
      timestamp: new Date().toISOString(),
      verified: false,
      falsePositive: false,
      sourceUrl: 'https://t.me/Binance_Announcements/12345',
    },
    {
      ticker: 'TEST',
      source: 'Twitter',
      timestamp: new Date().toISOString(),
      verified: true,
      falsePositive: false,
      sourceUrl: 'https://twitter.com/BinanceWallet/status/12345',
    },
    {
      ticker: 'DEMO',
      source: 'Binance',
      timestamp: new Date().toISOString(),
      verified: false,
      falsePositive: false,
      sourceUrl: 'https://www.binance.com/en/support/announcement/12345',
    },
  ];

  logger.info('Sending sample token notification', { tokenCount: sampleTokens.length });

  // Send notification with proper error handling
  try {
    const success = await sendTokenNotification(sampleTokens);

    if (!success) {
      // If the notification service returns false, throw a specific error
      throw AppError.externalService(
        'Failed to send notification',
        {
          service: 'telegram',
          tokenCount: sampleTokens.length
        }
      );
    }

    logger.info('Sample notification sent successfully');

    return NextResponse.json({
      success: true,
      message: 'Notification sent successfully',
      tokens: sampleTokens,
      requestId,
    });
  } catch (error) {
    // The createApiHandler will handle this error and convert it to a proper response
    if (error instanceof AppError) {
      throw error;
    }

    // Convert other errors to AppError
    throw AppError.externalService(
      'Error sending notification',
      { service: 'telegram' },
      error instanceof Error ? error : new Error(String(error))
    );
  }
});
