import { NextResponse } from 'next/server';
import {
  getTelegramHistory,
  getTwitterHistory,
  getBinanceHistory,
} from '@/utils/historyManager';
import { TelegramMessage, TwitterTweet, BinanceAnnouncement } from '@/lib/types';
import { saveTelegramMessages, saveTwitterTweets, saveBinanceAnnouncements } from '@/lib/supabaseService';

export async function GET() {
  try {
    console.log('Starting database cleanup...');
    console.log('API endpoint: /api/cleanup called');

    // Get all history
    console.log('Fetching history data...');
    const [telegramHistory, twitterHistory, binanceHistory] = await Promise.all([
      getTelegramHistory(),
      getTwitterHistory(),
      getBinanceHistory()
    ]);

    console.log('History data fetched successfully');

    console.log(`Current history counts: ${telegramHistory.length} Telegram messages, ${twitterHistory.length} tweets, ${binanceHistory.length} Binance announcements`);

    // Deduplicate Telegram messages by ID
    const uniqueTelegramMessages: TelegramMessage[] = [];
    const telegramIds = new Set<number>();

    for (const message of telegramHistory) {
      if (!telegramIds.has(message.id)) {
        telegramIds.add(message.id);
        uniqueTelegramMessages.push(message);
      }
    }

    // Deduplicate Twitter tweets by ID
    const uniqueTwitterTweets: TwitterTweet[] = [];
    const twitterIds = new Set<string>();

    for (const tweet of twitterHistory) {
      if (!twitterIds.has(tweet.id)) {
        twitterIds.add(tweet.id);
        uniqueTwitterTweets.push(tweet);
      }
    }

    // Deduplicate Binance announcements by URL
    const uniqueBinanceAnnouncements: BinanceAnnouncement[] = [];
    const binanceUrls = new Set<string>();

    for (const announcement of binanceHistory) {
      if (announcement.url && !binanceUrls.has(announcement.url)) {
        binanceUrls.add(announcement.url);
        uniqueBinanceAnnouncements.push(announcement);
      } else if (!announcement.url) {
        // Keep announcements without URLs but don't deduplicate them
        uniqueBinanceAnnouncements.push(announcement);
      }
    }

    // Save deduplicated history
    await Promise.all([
      saveTelegramMessages(uniqueTelegramMessages),
      saveTwitterTweets(uniqueTwitterTweets),
      saveBinanceAnnouncements(uniqueBinanceAnnouncements)
    ]);

    console.log(`After deduplication: ${uniqueTelegramMessages.length} Telegram messages, ${uniqueTwitterTweets.length} tweets, ${uniqueBinanceAnnouncements.length} Binance announcements`);

    // Calculate removed duplicates
    const telegramDuplicates = telegramHistory.length - uniqueTelegramMessages.length;
    const twitterDuplicates = twitterHistory.length - uniqueTwitterTweets.length;
    const binanceDuplicates = binanceHistory.length - uniqueBinanceAnnouncements.length;
    const totalDuplicates = telegramDuplicates + twitterDuplicates + binanceDuplicates;

    return NextResponse.json({
      success: true,
      message: 'Database cleanup completed successfully',
      stats: {
        telegramBefore: telegramHistory.length,
        telegramAfter: uniqueTelegramMessages.length,
        telegramDuplicatesRemoved: telegramDuplicates,

        twitterBefore: twitterHistory.length,
        twitterAfter: uniqueTwitterTweets.length,
        twitterDuplicatesRemoved: twitterDuplicates,

        binanceBefore: binanceHistory.length,
        binanceAfter: uniqueBinanceAnnouncements.length,
        binanceDuplicatesRemoved: binanceDuplicates,

        totalDuplicatesRemoved: totalDuplicates
      }
    });
  } catch (error) {
    console.error('Error in database cleanup:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Database cleanup failed',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
