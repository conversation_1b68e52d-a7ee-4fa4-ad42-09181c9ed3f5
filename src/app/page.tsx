import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getLatestScrapeResult, getTokenCount } from '@/lib/supabaseService';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { RotateCw } from 'lucide-react';
import SourceBadge from '@/components/SourceBadge';

// Function to format date
function formatDate(dateString?: string) {
  if (!dateString) return 'Unknown date';
  const date = new Date(dateString);
  return date.toLocaleString();
}

export default async function Home() {
  const scrapeResults = await getLatestScrapeResult();
  const tokenCount = await getTokenCount();

  console.log('[INFO]: Rendering home page', {
    hasResults: !!scrapeResults,
    tokenCount,
    scrapeTime: scrapeResults?.fetchedAt
  });

  return (
    <div className="min-h-screen p-6 pb-20 sm:p-8 max-w-7xl mx-auto">
      <Header tokenCount={tokenCount} />

      <main className="flex flex-col gap-8">
        {scrapeResults ? (
          <>
            <Card>
              <CardHeader>
                <CardTitle>Latest Scrape</CardTitle>
                <CardDescription>
                  Fetched at: {formatDate(scrapeResults.fetchedAt)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="telegram" className="w-full">
                  <TabsList className="w-full justify-start mb-4">
                    <TabsTrigger value="telegram">
                      Telegram ({scrapeResults.telegram.length})
                    </TabsTrigger>
                    <TabsTrigger value="twitter">
                      Twitter ({scrapeResults.twitter.length})
                    </TabsTrigger>
                    <TabsTrigger value="binance">
                      Binance ({scrapeResults.announcements.length})
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="telegram" className="space-y-4">
                    {scrapeResults.telegram.length > 0 ? (
                      <div className="rounded-md border">
                        <div className="divide-y">
                          {scrapeResults.telegram.map((msg) => (
                            <div key={msg.id} className="p-4">
                              <a href={msg.url || msg.link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline block mb-2">
                                {msg.text.substring(0, 100)}...
                              </a>
                              <div className="flex items-center justify-between">
                                <Badge variant="telegram">Telegram</Badge>
                                <span className="text-xs text-muted-foreground">{formatDate(msg.date?.toString())}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No Telegram messages found in the latest scrape.</p>
                    )}
                  </TabsContent>

                  <TabsContent value="twitter" className="space-y-4">
                    {scrapeResults.twitter.length > 0 ? (
                      <div className="rounded-md border">
                        <div className="divide-y">
                          {scrapeResults.twitter.map((tweet) => (
                            <div key={tweet.id} className="p-4">
                              <a href={tweet.url || tweet.link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline block mb-2">
                                {tweet.text.substring(0, 100)}...
                              </a>
                              <div className="flex items-center justify-between">
                                <Badge variant="twitter">Twitter</Badge>
                                <span className="text-xs text-muted-foreground">{formatDate(tweet.created_at)}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No Twitter posts found in the latest scrape.</p>
                    )}
                  </TabsContent>

                  <TabsContent value="binance" className="space-y-4">
                    {scrapeResults.announcements.length > 0 ? (
                      <div className="rounded-md border">
                        <div className="divide-y">
                          {scrapeResults.announcements.map((ann) => (
                            <div key={ann.id} className="p-4">
                              <a href={ann.url || ann.link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline block mb-2 font-medium">
                                {ann.title}
                              </a>
                              <div className="flex items-center justify-between">
                                <Badge variant="binance">Binance</Badge>
                                <span className="text-xs text-muted-foreground">{formatDate(ann.date)}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No Binance announcements found in the latest scrape.</p>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Newly Discovered Tokens</CardTitle>
                <CardDescription>
                  Tokens discovered in the latest scrape
                </CardDescription>
              </CardHeader>
              <CardContent>
                {scrapeResults.addedTokens.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Ticker</TableHead>
                        <TableHead>Source</TableHead>
                        <TableHead>Discovered At</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {scrapeResults.addedTokens.map((token, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium text-primary">{token.ticker}</TableCell>
                          <TableCell>
                            <SourceBadge
                              source={token.source}
                              sourceUrl={token.sourceUrl}
                            />
                          </TableCell>
                          <TableCell>{formatDate(token.timestamp)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-muted-foreground">No new tokens discovered in the latest scrape.</p>
                )}
              </CardContent>
            </Card>
          </>
        ) : (
          <Card className="text-center p-6">
            <CardHeader>
              <CardTitle>No Scrape Results Yet</CardTitle>
              <CardDescription>
                The scraper hasn&apos;t run yet or no results have been saved.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="success" size="lg" className="mt-4">
                <Link href="/api/scrape" className="flex items-center gap-2">
                  <RotateCw className="h-5 w-5" />
                  Run Scraper Now
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </main>

      <Footer />
    </div>
  );
}
