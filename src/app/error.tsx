'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 text-center">
      <div className="bg-red-50 dark:bg-red-900/20 p-8 rounded-lg max-w-2xl">
        <h1 className="text-3xl font-bold mb-4 text-red-600 dark:text-red-400">Something went wrong</h1>
        <p className="mb-6 text-gray-700 dark:text-gray-300">
          We encountered an error while processing your request. This could be due to missing API keys or temporary service issues.
        </p>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-md mb-6 text-left overflow-auto max-h-40">
          <pre className="text-sm text-red-500 dark:text-red-400 whitespace-pre-wrap">
            {error.message || 'Unknown error occurred'}
          </pre>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => reset()}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
          >
            Try again
          </button>
          <Link
            href="/"
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            Return to dashboard
          </Link>
        </div>
      </div>

      <div className="mt-8 text-gray-500 text-sm max-w-2xl">
        <h2 className="font-medium mb-2">Troubleshooting Tips:</h2>
        <ul className="list-disc list-inside text-left space-y-1">
          <li>Check that all required API keys are set in your .env file</li>
          <li>Ensure your Telegram session string is valid and not expired</li>
          <li>Verify that your Twitter bearer token has the correct permissions</li>
          <li>Check if you&apos;ve exceeded any API rate limits</li>
          <li>Ensure your OpenRouter account has sufficient credits</li>
        </ul>
      </div>
    </div>
  );
}
