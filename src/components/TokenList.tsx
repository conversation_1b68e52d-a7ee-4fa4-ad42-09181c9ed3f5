'use client';

import { useState } from 'react';
import { Token } from '@/lib/types';
import TokenActions from './TokenActions';
import TokenFilters from './TokenFilters';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';

interface TokenListProps {
  tokens: Token[];
}

// Function to format date
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleString();
}

export default function TokenList({ tokens: initialTokens }: TokenListProps) {
  const [tokens] = useState<Token[]>(initialTokens);
  const [filteredTokens, setFilteredTokens] = useState<Token[]>(initialTokens);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Filter and sort tokens
  const handleSearch = (searchTerm: string) => {
    if (!searchTerm) {
      setFilteredTokens(tokens);
      return;
    }

    const filtered = tokens.filter(token =>
      token.ticker.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.source.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredTokens(filtered);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleSortChange = (sortOption: string) => {
    let sorted = [...filteredTokens];

    switch (sortOption) {
      case 'newest':
        sorted = sorted.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        break;
      case 'oldest':
        sorted = sorted.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        break;
      case 'a-z':
        sorted = sorted.sort((a, b) => a.ticker.localeCompare(b.ticker));
        break;
      case 'z-a':
        sorted = sorted.sort((a, b) => b.ticker.localeCompare(a.ticker));
        break;
      default:
        break;
    }

    setFilteredTokens(sorted);
  };

  const handleFilterChange = (filterOption: string) => {
    if (filterOption === 'all') {
      setFilteredTokens(tokens);
      return;
    }

    const filtered = tokens.filter(token =>
      token.source.toLowerCase().includes(filterOption.toLowerCase())
    );

    setFilteredTokens(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTokens.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil(filteredTokens.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      <TokenFilters
        onSearch={handleSearch}
        onSortChange={handleSortChange}
        onFilterChange={handleFilterChange}
      />

      <Card>
        <div className="rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ticker</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Discovered At</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentItems.map((token, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium text-primary">{token.ticker}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        token.source.toLowerCase().includes('binance')
                          ? 'binance'
                          : token.source.toLowerCase().includes('telegram')
                          ? 'telegram'
                          : token.source.toLowerCase().includes('twitter')
                          ? 'twitter'
                          : 'secondary'
                      }
                      className={token.sourceUrl ? 'cursor-pointer' : ''}
                      onClick={token.sourceUrl ? () => window.open(token.sourceUrl, '_blank', 'noopener,noreferrer') : undefined}
                    >
                      {token.source}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(token.timestamp)}</TableCell>
                  <TableCell>
                    {token.verified ? (
                      <Badge variant="success" className="flex items-center gap-1">
                        <Check className="w-3 h-3" />
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        Unverified
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <TokenActions token={token} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">{Math.min(indexOfLastItem, filteredTokens.length)}</span> of{' '}
                <span className="font-medium">{filteredTokens.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-l-md"
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum = currentPage;
                  if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  if (pageNum > 0 && pageNum <= totalPages) {
                    return (
                      <Button
                        key={i}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="icon"
                        className="rounded-none"
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    );
                  }
                  return null;
                })}
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-r-md"
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
