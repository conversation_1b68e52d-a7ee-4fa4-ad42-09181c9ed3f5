'use client';

import { useState } from 'react';
import { Token } from '@/lib/types';
import { Button } from './ui/button';
import { Link2, Globe, CheckCircle, Trash2 } from 'lucide-react';

// Loading spinner component
const LoadingSpinner = () => (
  <div className="inline-block animate-spin h-3 w-3 border-2 border-current border-t-transparent rounded-full"
       aria-hidden="true">
  </div>
);

interface TokenActionsProps {
  token: Token;
}

export default function TokenActions({ token }: TokenActionsProps) {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  const handleMarkFalsePositive = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (confirm(`Are you sure you want to mark ${token.ticker} as a false positive? It will be hidden from the list and excluded from future scrapes.`)) {
      setLoadingAction('falsePositive');
      console.log(`[INFO]: Marking token as false positive`, { ticker: token.ticker });

      try {
        const response = await fetch('/api/tokens/mark-false-positive', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ticker: token.ticker }),
        });

        const data = await response.json();

        if (data.success) {
          console.log(`[SUCCESS]: Token marked as false positive`, { ticker: token.ticker });
          alert(`${token.ticker} marked as false positive`);
          window.location.reload();
        } else {
          console.error(`[ERROR]: Failed to mark token as false positive`, {
            ticker: token.ticker,
            error: data.message
          });
          alert(`Error: ${data.message}`);
        }
      } catch (error) {
        console.error('[ERROR]: Error marking token as false positive:', error);
        alert('An error occurred. Please try again.');
      } finally {
        setLoadingAction(null);
      }
    }
  };

  const handleVerifyToken = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (confirm(`Verify ${token.ticker} as a legitimate token?`)) {
      setLoadingAction('verify');
      console.log(`[INFO]: Verifying token`, { ticker: token.ticker });

      try {
        const response = await fetch('/api/tokens/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ticker: token.ticker, verified: true }),
        });

        const data = await response.json();

        if (data.success) {
          console.log(`[SUCCESS]: Token verified successfully`, { ticker: token.ticker });
          alert(`${token.ticker} verified successfully`);
          window.location.reload();
        } else {
          console.error(`[ERROR]: Failed to verify token`, {
            ticker: token.ticker,
            error: data.message
          });
          alert(`Error: ${data.message}`);
        }
      } catch (error) {
        console.error('[ERROR]: Error verifying token:', error);
        alert('An error occurred. Please try again.');
      } finally {
        setLoadingAction(null);
      }
    }
  };

  const handleOpenSource = () => {
    console.log(`[INFO]: Opening source for token`, { ticker: token.ticker });

    // Check if the token has a sourceUrl property
    if (!token.sourceUrl) {
      console.warn(`[WARN]: No source URL available for token`, { ticker: token.ticker });
      alert('Source link not available for this token.');
      return;
    }

    try {
      window.open(token.sourceUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('[ERROR]: Error opening source link:', error);
      alert('Failed to open source link. Please check your browser settings or try again.');
    }
  };

  const handleOpenCoinGecko = () => {
    console.log(`[INFO]: Opening CoinGecko for token`, { ticker: token.ticker });
    try {
      window.open(`https://www.coingecko.com/en/coins/${token.ticker.toLowerCase()}`, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('[ERROR]: Error opening CoinGecko:', error);
      alert('Failed to open CoinGecko. Please check your browser settings or try again.');
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="ghost"
        size="icon"
        onClick={handleOpenSource}
        title={token.sourceUrl ? "View Source" : "No Source Available"}
        disabled={!token.sourceUrl}
        className="h-8 w-8"
      >
        <Link2 className="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={handleOpenCoinGecko}
        title="View on CoinGecko"
        className="h-8 w-8"
      >
        <Globe className="h-4 w-4" />
      </Button>

      {!token.verified && (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleVerifyToken}
          disabled={loadingAction !== null}
          title="Verify Token"
          className="h-8 w-8 text-green-600 hover:text-green-700 dark:text-green-500 dark:hover:text-green-400"
        >
          {loadingAction === 'verify' ? (
            <LoadingSpinner />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
        </Button>
      )}

      <Button
        variant="ghost"
        size="icon"
        onClick={handleMarkFalsePositive}
        disabled={loadingAction !== null}
        title="Mark as false positive"
        className="h-8 w-8 text-destructive hover:text-destructive/80"
      >
        {loadingAction === 'falsePositive' ? (
          <LoadingSpinner />
        ) : (
          <Trash2 className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
