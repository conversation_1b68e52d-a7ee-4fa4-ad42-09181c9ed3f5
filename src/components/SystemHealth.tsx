'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Refresh<PERSON><PERSON>, CheckCircle, AlertTriangle, XCircle, Clock } from 'lucide-react';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  details?: Record<string, unknown>;
  lastChecked: string;
  responseTime?: number;
}

interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: HealthCheckResult[];
  summary: {
    healthy: number;
    degraded: number;
    unhealthy: number;
    total: number;
  };
  timestamp: string;
}

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'healthy':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'degraded':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'unhealthy':
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Clock className="h-4 w-4 text-gray-500" />;
  }
};

const StatusBadge = ({ status }: { status: string }) => {
  const variant = status === 'healthy' ? 'success' : status === 'degraded' ? 'warning' : 'destructive';
  return (
    <Badge variant={variant} className="capitalize">
      {status}
    </Badge>
  );
};

export default function SystemHealth() {
  const [healthData, setHealthData] = useState<SystemHealthStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchHealthData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      
      if (data.success) {
        setHealthData(data.data);
        setLastUpdated(new Date());
      } else {
        setError(data.error?.message || 'Failed to fetch health data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatResponseTime = (responseTime?: number) => {
    if (!responseTime) return 'N/A';
    return `${responseTime}ms`;
  };

  const formatDetails = (details?: Record<string, unknown>) => {
    if (!details || Object.keys(details).length === 0) return null;
    
    return (
      <div className="mt-2 text-xs text-muted-foreground">
        <details className="cursor-pointer">
          <summary className="hover:text-foreground">View Details</summary>
          <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
            {JSON.stringify(details, null, 2)}
          </pre>
        </details>
      </div>
    );
  };

  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            Health Check Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchHealthData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading System Health...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <Card className={`border-2 ${
        healthData.overall === 'healthy' ? 'border-green-200' :
        healthData.overall === 'degraded' ? 'border-yellow-200' : 'border-red-200'
      }`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon status={healthData.overall} />
              System Health Status
            </div>
            <div className="flex items-center gap-2">
              <StatusBadge status={healthData.overall} />
              <Button
                variant="outline"
                size="sm"
                onClick={fetchHealthData}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{healthData.summary.healthy}</div>
              <div className="text-sm text-muted-foreground">Healthy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{healthData.summary.degraded}</div>
              <div className="text-sm text-muted-foreground">Degraded</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{healthData.summary.unhealthy}</div>
              <div className="text-sm text-muted-foreground">Unhealthy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{healthData.summary.total}</div>
              <div className="text-sm text-muted-foreground">Total</div>
            </div>
          </div>
          <div className="text-xs text-muted-foreground">
            Last updated: {lastUpdated ? lastUpdated.toLocaleString() : 'Never'}
          </div>
        </CardContent>
      </Card>

      {/* Individual Services */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {healthData.services.map((service) => (
          <Card key={service.service} className={`border ${
            service.status === 'healthy' ? 'border-green-200' :
            service.status === 'degraded' ? 'border-yellow-200' : 'border-red-200'
          }`}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-base">
                <div className="flex items-center gap-2">
                  <StatusIcon status={service.status} />
                  <span className="capitalize">{service.service.replace('-', ' ')}</span>
                </div>
                <StatusBadge status={service.status} />
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm mb-2">{service.message}</p>
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Response: {formatResponseTime(service.responseTime)}</span>
                <span>{formatTimestamp(service.lastChecked)}</span>
              </div>
              {formatDetails(service.details)}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
