"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import ThemeToggle from "./ThemeToggle";
import { Button } from "./ui/button";
import { RefreshCw, RotateCw, Trash2, ArrowLeft } from "lucide-react";

interface HeaderProps {
  tokenCount?: number;
}

export default function Header({ tokenCount = 0 }: HeaderProps) {
  const pathname = usePathname();
  const isTokensPage = pathname === "/tokens";

  return (
    <header className="mb-10">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold">
            {isTokensPage ? "Token Tracker" : "Binance Token Scraper"}
          </h1>
          <ThemeToggle />
        </div>
        <div className="flex flex-wrap gap-3">
          {!isTokensPage && (
            <Button asChild variant="default">
              <Link href="/tokens">
                Token Tracker {tokenCount > 0 && `(${tokenCount})`}
              </Link>
            </Button>
          )}
          <Button asChild variant="success">
            <Link href="/api/scrape" className="flex items-center gap-2">
              <RotateCw className="h-4 w-4" />
              Run Scraper
            </Link>
          </Button>
          {isTokensPage && (
            <>
              <Button asChild variant="info">
                <Link href="/api/tokens/refresh" className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Refresh Tokens
                </Link>
              </Button>
              <Button asChild variant="warning">
                <Link href="/api/cleanup" className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4" />
                  Remove Duplicates
                </Link>
              </Button>
              <Button asChild variant="secondary">
                <Link href="/" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Dashboard
                </Link>
              </Button>
            </>
          )}
        </div>
      </div>
      <p className="text-muted-foreground">
        {isTokensPage
          ? `Tracking ${tokenCount} cryptocurrency tokens discovered by the scraper`
          : "Automatically scrapes Telegram, Twitter, and Binance announcements for new token listings"}
      </p>
    </header>
  );
}
