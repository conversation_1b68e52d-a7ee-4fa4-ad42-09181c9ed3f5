'use client';

import { useState } from 'react';
import { Search, SortAsc } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface TokenFiltersProps {
  onSearch: (searchTerm: string) => void;
  onSortChange: (sortOption: string) => void;
  onFilterChange: (filterOption: string) => void;
}

export default function TokenFilters({ onSearch, onSortChange, onFilterChange }: TokenFiltersProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onSortChange(e.target.value);
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange(e.target.value);
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-grow">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-muted-foreground" />
            </div>
            <input
              type="search"
              id="token-search"
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full p-2.5 pl-10 text-sm border border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:border-input"
              placeholder="Search tokens..."
            />
          </div>

          <div className="flex gap-2">
            <div className="relative">
              <select
                id="sort-by"
                onChange={handleSortChange}
                className="bg-background border border-input text-foreground text-sm rounded-md focus:ring-2 focus:ring-ring focus:border-input block p-2.5 pr-8 appearance-none"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="a-z">A-Z</option>
                <option value="z-a">Z-A</option>
              </select>
              <SortAsc className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
            </div>

            <select
              id="filter-by"
              onChange={handleFilterChange}
              className="bg-background border border-input text-foreground text-sm rounded-md focus:ring-2 focus:ring-ring focus:border-input block p-2.5"
            >
              <option value="all">All Sources</option>
              <option value="telegram">Telegram</option>
              <option value="twitter">Twitter</option>
              <option value="binance">Binance</option>
              <option value="unknown">Unknown</option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
