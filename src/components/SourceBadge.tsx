'use client';

import { Badge } from '@/components/ui/badge';

interface SourceBadgeProps {
  source: string;
  sourceUrl?: string | null;
}

export default function SourceBadge({ source, sourceUrl }: SourceBadgeProps) {
  const handleClick = () => {
    if (sourceUrl) {
      window.open(sourceUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const variant = 
    source.toLowerCase().includes('binance') 
      ? 'binance' 
      : source.toLowerCase().includes('telegram') 
      ? 'telegram' 
      : source.toLowerCase().includes('twitter')
      ? 'twitter'
      : 'secondary';

  return (
    <Badge
      variant={variant}
      className={sourceUrl ? "cursor-pointer" : ""}
      onClick={sourceUrl ? handleClick : undefined}
    >
      {source}
    </Badge>
  );
}
