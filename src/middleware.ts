import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { generateRequestId, setRequestId } from '@/lib/logger';

/**
 * Middleware to add request ID to all API requests
 * This helps with tracking requests across the application
 */
export function middleware(request: NextRequest) {
  // Only process API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Generate a unique request ID
    const requestId = generateRequestId();
    
    // Set the request ID in the logger
    setRequestId(requestId);
    
    // Add the request ID to the response headers
    const response = NextResponse.next();
    response.headers.set('x-request-id', requestId);
    
    return response;
  }
  
  return NextResponse.next();
}

/**
 * Configure which paths should be processed by the middleware
 */
export const config = {
  matcher: '/api/:path*',
};
