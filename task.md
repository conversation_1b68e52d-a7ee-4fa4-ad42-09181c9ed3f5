# Binance Token Scraper - Implementation Tasks

## Overview
This document outlines the implementation tasks for building a Next.js application that scrapes cryptocurrency token information from multiple sources (Telegram, Twitter, and Binance announcements) and uses AI to detect new token listings.

## Project Structure

```
binance-bot/
├── .env.example           # Example environment variables
├── .env                   # Environment variables (gitignored)
├── data/                  # Data storage
│   ├── tokens.json        # Database of known tokens
│   └── crypto_announcements.json # Hourly scrape results
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── scrape/
│   │   │       └── route.ts  # API route for scraping (cron job target)
│   │   ├── page.tsx          # Main dashboard page
│   │   └── layout.tsx        # App layout
│   ├── lib/
│   │   ├── utils.ts          # Utility functions
│   │   └── types.ts          # TypeScript type definitions
│   └── utils/
│       ├── telegram.ts       # Telegram scraping functionality
│       ├── twitter.ts        # Twitter scraping functionality
│       ├── binance.ts        # Binance scraping functionality
│       └── analyzeTokens.ts  # AI token analysis
├── vercel.json            # Vercel configuration (cron jobs)
└── package.json           # Project dependencies
```

## Implementation Tasks

### 1. Project Setup

- [x] Initialize Next.js project with TypeScript and TailwindCSS
- [x] Create `.env.example` and `.env` files with required API keys
- [x] Install required dependencies:
  - [x] `telegram` (GramJS)
  - [x] `twitter-api-v2`
  - [x] `@mendable/firecrawl-js`
  - [x] `ai` (Vercel AI SDK)
  - [x] `@openrouter/ai-sdk-provider`
  - [x] Other utility packages as needed

### 2. Data Structure Setup

- [x] Create `data` directory
- [x] Initialize `tokens.json` with empty array
- [x] Define TypeScript interfaces for all data structures

### 3. Telegram Integration

- [x] Create `utils/telegram.ts` with GramJS implementation
- [x] Implement authentication with API ID, hash, and session string
- [x] Create function to fetch 5 most recent messages from Binance channel
- [x] Add error handling and logging

### 4. Twitter Integration

- [x] Create `utils/twitter.ts` with twitter-api-v2 implementation
- [x] Implement authentication with bearer token
- [x] Create function to fetch 5 most recent tweets from BinanceWallet
- [x] Add error handling and logging

### 5. Binance Announcements Integration

- [x] Create `utils/binance.ts` with Firecrawl implementation
- [x] Implement scraping of Binance "New Cryptocurrency Listing" page
- [x] Extract and process the 5 most recent announcements
- [x] Add error handling and logging

### 6. AI Token Analysis

- [x] Create `utils/analyzeTokens.ts` for token extraction
- [x] Implement OpenRouter + Vercel AI SDK integration
- [x] Create prompt for Gemini model to extract new tokens
- [x] Implement token validation (regex check for valid tickers)
- [x] Add deduplication against known tokens
- [x] Implement error handling with fallback

### 7. API Route Implementation

- [x] Create `/api/scrape/route.ts` endpoint
- [x] Implement orchestration of all scraping functions
- [x] Combine results and process with AI token analysis
- [x] Save results to `crypto_announcements.json`
- [x] Add comprehensive error handling and logging

### 8. Vercel Configuration

- [x] Create `vercel.json` with cron job configuration
- [x] Set up hourly schedule for the scrape endpoint

### 9. Dashboard UI

- [x] Update `page.tsx` to display recent scrape results
- [x] Create simple UI to view discovered tokens
- [x] Add timestamp information and source attribution
- [x] Create dedicated token tracker page at `/tokens`
- [x] Add navigation between dashboard and token tracker
- [x] Add prominent "Run Scraper" buttons throughout the UI

### 10. Testing & Validation

- [ ] Test each scraping function individually
- [ ] Test the complete scraping pipeline
- [ ] Validate token extraction accuracy
- [ ] Test error handling scenarios

### 11. Error Handling & UI Improvements

- [x] Fix library import issues (Firecrawl and OpenRouter)
- [x] Add error boundary and error page
- [x] Add loading states for better UX
- [x] Create example data files for testing without API keys
- [x] Update documentation with troubleshooting information
- [x] Remove mockup data from the repository
- [x] Fix API integration issues with proper error handling
- [x] Fix Twitter API initialization error with dynamic imports
- [x] Remove mock clients and implement graceful degradation
- [x] Implement proper OpenRouter AI integration for token extraction
- [x] Add proper error handling for all external API calls
- [x] Create Telegram session string generator script

## Environment Variables

The following environment variables are required:

- `TG_API_ID` - Telegram API ID
- `TG_API_HASH` - Telegram API Hash
- `TG_SESSION_STRING` - Telegram session string
- `TW_BEARER` - Twitter API bearer token
- `FIRECRAWL_API_KEY` - Firecrawl API key
- `OPENROUTER_API_KEY` - OpenRouter API key

## Notes

- All scraping is limited to 5 items per source to stay within free API limits
- The system runs hourly via Vercel cron jobs
- Token deduplication ensures each token is recorded only once
- Error handling ensures the system continues to function even if one source fails
