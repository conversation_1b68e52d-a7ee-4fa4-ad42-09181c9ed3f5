# CLAUDE.md - Binance Token Scraper Assistant Guide

## 🚀 Quick Start

```bash
npm run dev                                # Start dev server with Turbopack
npm run build                            # Build for production
npm run start                            # Start production server
npm run lint                             # Run ESLint

# Testing & Setup Scripts
npx tsx scripts/setup-supabase.ts        # Set up Supabase database
npx tsx scripts/test-supabase-connection.ts  # Test Supabase connection
npx tsx scripts/migrate-to-supabase.ts   # Migrate existing data to Supabase
```

## 🎯 Project Overview

**Binance Token Scraper** is a Next.js application that automatically discovers new cryptocurrency tokens by scraping multiple sources and using AI for intelligent analysis.

### Core Functionality
- **Multi-Source Scraping**: Telegram (Binance channel), Twitter (BinanceWallet), Binance announcements
- **AI-Powered Analysis**: Uses OpenRouter + Gemini 2.5 Flash to extract token tickers from scraped content
- **Smart Deduplication**: Prevents duplicate token entries across sources
- **Automated Notifications**: Telegram bot notifications for new token discoveries
- **Hourly Automation**: Vercel cron jobs run the scraper every hour
- **Token Tracking**: Comprehensive dashboard and dedicated token tracker

## 🛠️ Tech Stack

### Core Framework & Language
- **Next.js 15.3.2** - React framework with App Router
- **TypeScript 5** - Type safety with strict mode
- **React 19** - Latest React with modern features

### Styling & UI
- **TailwindCSS 3.3** - Utility-first CSS framework
- **Shadcn/ui** - High-quality component library (New York style)
- **Lucide React** - Icon library
- **Next Themes** - Dark/light mode support
- **Geist Font** - Modern typography (Sans & Mono)

### Data & Storage
- **Supabase** - PostgreSQL database with real-time features
- **Supabase Client 2.49** - Database operations and authentication
- **Local JSON Fallback** - File-based storage for development

### Scraping & External APIs
- **GramJS (telegram 2.26.22)** - Telegram MTProto client
- **twitter-api-v2 1.23.2** - Twitter API v2 client
- **Firecrawl 1.21.1** - Web scraping service
- **OpenRouter AI SDK** - AI provider for multiple models
- **Vercel AI SDK 4.3.16** - Unified AI interface

### AI & Analysis
- **OpenRouter Provider** - Access to multiple AI models
- **Gemini 2.5 Flash** - Primary model for token extraction
- **Enhanced Regex Fallback** - Backup token detection when AI unavailable

### Development & Deployment
- **Turbopack** - Fast development bundler
- **ESLint 9** - Code linting with Next.js config
- **Vercel** - Deployment platform with cron jobs
- **Bun** - Fast package manager and runtime (optional)

## 📁 Project Structure

```
/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/
│   │   │   ├── scrape/         # Main scraping endpoint
│   │   │   ├── cleanup/        # Database cleanup
│   │   │   ├── notify/         # Telegram notifications
│   │   │   └── tokens/         # Token management endpoints
│   │   ├── tokens/             # Token tracker page
│   │   ├── layout.tsx          # Root layout with themes
│   │   ├── page.tsx            # Dashboard homepage
│   │   ├── error.tsx           # Error boundary
│   │   └── loading.tsx         # Loading states
│   ├── components/
│   │   ├── ui/                 # Shadcn/ui components
│   │   ├── Header.tsx          # App header with navigation
│   │   ├── Footer.tsx          # App footer
│   │   ├── TokenList.tsx       # Token display component
│   │   ├── TokenFilters.tsx    # Token filtering controls
│   │   ├── TokenActions.tsx    # Token action buttons
│   │   ├── ThemeProvider.tsx   # Theme context provider
│   │   ├── ThemeToggle.tsx     # Dark/light mode toggle
│   │   └── SourceBadge.tsx     # Source attribution badges
│   ├── lib/                    # Core utilities and services
│   │   ├── supabase.ts         # Supabase client setup
│   │   ├── supabaseService.ts  # Database operations
│   │   ├── database.types.ts   # Supabase type definitions
│   │   ├── types.ts            # Application type definitions
│   │   ├── utils.ts            # General utilities
│   │   ├── logger.ts           # Structured logging
│   │   ├── errorUtils.ts       # Error handling utilities
│   │   ├── memoryUtils.ts      # Memory management
│   │   ├── retryUtils.ts       # Retry logic
│   │   ├── cacheUtils.ts       # Caching mechanisms
│   │   └── dbUtils.ts          # Database utilities
│   ├── utils/                  # Scraping and analysis
│   │   ├── telegram.ts         # Telegram scraping (GramJS)
│   │   ├── twitter.ts          # Twitter scraping (API v2)
│   │   ├── binance.ts          # Binance announcements (Firecrawl)
│   │   ├── analyzeTokens.ts    # AI token extraction
│   │   ├── notifyTelegram.ts   # Telegram notifications
│   │   └── historyManager.ts   # Deduplication logic
│   └── middleware.ts           # Next.js middleware
├── scripts/                    # Setup and maintenance scripts
│   ├── setup-supabase.ts       # Database schema setup
│   ├── test-supabase-connection.ts  # Connection testing
│   └── migrate-to-supabase.ts  # Data migration
├── data/                       # Local data storage (fallback)
│   ├── tokens.json             # Known tokens database
│   └── crypto_announcements.json  # Latest scrape results
├── public/                     # Static assets
├── supabase_setup.sql          # Database schema and policies
├── vercel.json                 # Vercel configuration (cron jobs)
├── SUPABASE_SETUP.md           # Database setup guide
├── SUPABASE_TROUBLESHOOTING.md # Database troubleshooting
└── serverless-migration-summary.md  # Migration notes
```

## 🎨 Design System

### Typography
Uses **Geist Font Family** with 2 variants:
- **Geist Sans** - Clean, modern sans-serif for UI
- **Geist Mono** - Technical monospace for code/data

### Color Palette
Following **60/30/10 Rule**:
- **60% Neutral**: Backgrounds and surfaces (HSL CSS variables)
- **30% Complementary**: Text and UI elements
- **10% Accent**: Brand colors and highlights

**Brand Colors**:
- **Binance**: `#F0B90B` (official yellow)
- **Telegram**: `#0088cc` (blue)
- **Twitter**: `#1DA1F2` (blue)
- **Success**: `#10B981` (green)
- **Warning**: `#F59E0B` (amber)

### Component System
- **Shadcn/ui**: High-quality, accessible components
- **Radix UI**: Headless component primitives
- **Class Variance Authority**: Type-safe variant handling
- **Tailwind Merge**: Conflict-free utility merging

## 🔧 Development Patterns

### TypeScript Standards
```typescript
// Strict mode enabled - no 'any' types allowed
interface Token {
  ticker: string;
  source: string;
  timestamp: string;
  verified?: boolean;
  falsePositive?: boolean;
  sourceUrl?: string;
}

// Use proper error handling
try {
  const result = await riskyOperation();
  return result;
} catch (error) {
  logger.error('Operation failed', error instanceof Error ? error : new Error(String(error)));
  throw AppError.internal('Failed to process', { context }, error);
}
```

### API Route Pattern
```typescript
import { createApiHandler } from '@/lib/errorUtils';

export const GET = createApiHandler(async (req: Request) => {
  // Standardized error handling and logging
  // Memory usage tracking
  // Request ID correlation
});
```

### Database Operations
```typescript
import { getTokens, saveToken } from '@/lib/supabaseService';

// Always check if Supabase is configured
if (!isSupabaseConfigured()) {
  // Fallback to local storage
}

// Use proper error handling with context
try {
  await saveToken(token);
} catch (error) {
  logger.error('Failed to save token', error, { ticker: token.ticker });
}
```

## 🔐 Environment Variables

### Required for Full Functionality
```env
# Telegram API (my.telegram.org/apps)
TG_API_ID=your_telegram_api_id
TG_API_HASH=your_telegram_api_hash
TG_SESSION_STRING=your_session_string

# Twitter API (developer.twitter.com)
TW_BEARER=your_twitter_bearer_token

# Web Scraping (firecrawl.dev)
FIRECRAWL_API_KEY=your_firecrawl_api_key

# AI Analysis (openrouter.ai)
OPENROUTER_API_KEY=your_openrouter_api_key

# Telegram Notifications
NOTIFICATION_BOT_TOKEN=your_bot_token
NOTIFICATION_CHAT_ID=your_chat_id

# Supabase Database (supabase.com)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Source Configuration
BINANCE_CHANNEL=Binance_Announcements
BINANCE_TWITTER=BinanceWallet
BINANCE_ANNOUNCEMENTS_URL=https://www.binance.com/en/support/announcement/list/48
```

### Graceful Degradation
The application handles missing API keys gracefully:
- **Missing Telegram**: Skips Telegram scraping
- **Missing Twitter**: Skips Twitter scraping  
- **Missing Firecrawl**: Skips Binance announcements
- **Missing OpenRouter**: Falls back to enhanced regex
- **Missing Supabase**: Uses local JSON files

## 🤖 AI Token Analysis

### Primary: OpenRouter + Gemini 2.5 Flash
```typescript
const ai = openrouter('google/gemini-2.5-flash-preview-05-20');
const { text } = await generateText({
  model: ai,
  prompt: `Extract NEW crypto tickers (3-6 chars) from this text...
  
  Known tokens to EXCLUDE: ${knownTickers.join(', ')}
  
  Text: ${allText}
  
  Return only JSON array: ["TOKEN1", "TOKEN2"]`,
  temperature: 0.1,
  maxTokens: 200,
});
```

### Fallback: Enhanced Regex
```typescript
// Pattern matching for token listings
const tokenListingRegex = /(?:listing|trading for|new token)[:\s]+([A-Z0-9]{3,6})/gi;

// General token pattern if specific patterns fail
const tokenRegex = /[A-Z0-9]{3,6}/g;
```

### False Positive Filtering
```typescript
const COMMON_FALSE_POSITIVES = [
  'USD', 'BTC', 'ETH', 'BNB', 'USDC', 'FDUSD',
  'TGE', 'APR', 'BTW', 'APP', '888'
];
```

## 🔄 Data Flow

### Hourly Scraping Process
1. **Vercel Cron** triggers `/api/scrape` every hour
2. **Parallel Scraping** from 3 sources (5 items each)
3. **Deduplication** against historical data
4. **AI Analysis** extracts new token tickers
5. **Database Storage** saves results and new tokens
6. **Notifications** sends Telegram alerts for new finds

### Deduplication Strategy
- **Telegram**: By message ID and channel
- **Twitter**: By tweet ID and username
- **Binance**: By announcement ID and URL
- **Tokens**: By ticker symbol across all sources

## 📊 Database Schema

### Core Tables
- **tokens**: Token registry with metadata
- **scrape_results**: Hourly scrape summaries
- **telegram_messages**: Telegram post history
- **twitter_tweets**: Twitter post history  
- **binance_announcements**: Binance announcement history

### Key Features
- **Row Level Security (RLS)**: Secure data access
- **Indexes**: Optimized for common queries
- **JSON Columns**: Flexible metadata storage
- **Timestamps**: Created/updated tracking

## 🚨 Error Handling

### Structured Error System
```typescript
import { AppError } from '@/lib/errorUtils';

// Typed error creation
throw AppError.validation('Invalid token format', { ticker });
throw AppError.external('API rate limit exceeded', { service: 'twitter' });
throw AppError.internal('Database connection failed', { table: 'tokens' }, originalError);
```

### Comprehensive Logging
```typescript
import logger from '@/lib/logger';

logger.info('Starting operation', { context });
logger.warn('Degraded performance', null, { fallback: 'cache' });
logger.error('Operation failed', error, { retryAttempt: 3 });
logger.debug('Detailed debugging', { data });
```

### Memory Management
```typescript
import { logMemoryUsage, streamProcess } from '@/lib/memoryUtils';

// Track memory usage at key points
logMemoryUsage('After scraping');

// Process large data sets efficiently
await streamProcess(dataStream, processor, { chunkSize: 100 });
```

## 🧪 Testing & Validation

### Manual Testing
```bash
# Test individual components
curl http://localhost:3000/api/scrape
curl http://localhost:3000/api/notify/test

# Validate database connection
npx tsx scripts/test-supabase-connection.ts

# Check token detection accuracy
# Review AI responses in logs for false positives/negatives
```

### Data Validation
- **Token Format**: 3-6 uppercase alphanumeric characters
- **Source Attribution**: Every token linked to discovery source
- **Timestamp Accuracy**: ISO 8601 format with timezone
- **Deduplication**: No duplicate entries across time/sources

## 🚀 Deployment

### Vercel Configuration
```json
{
  "crons": [
    { "path": "/api/scrape", "schedule": "0 * * * *" }
  ],
  "env": {
    "NEXT_TELEMETRY_DISABLED": "1"
  }
}
```

### Performance Optimizations
- **Turbopack**: Fast development builds
- **Edge Runtime**: Serverless function optimization
- **Memory Streaming**: Handle large data sets efficiently
- **Connection Pooling**: Database performance

## 🛑 Common Issues & Solutions

### API Integration Problems
- **Telegram Session Expired**: Regenerate session string
- **Twitter Rate Limits**: Built-in exponential backoff
- **Firecrawl Quota**: Graceful degradation to skip scraping
- **OpenRouter Credits**: Automatic fallback to regex

### Database Issues
- **Connection Failures**: Automatic retry with backoff
- **Migration Problems**: Use provided setup scripts
- **Type Mismatches**: Regenerate types from Supabase

### Memory & Performance
- **Large Text Processing**: Streaming implementation
- **Memory Leaks**: Comprehensive cleanup in error handlers
- **Cold Starts**: Optimized initialization code

## 📝 Code Quality Standards

- **No `any` types** - Strict TypeScript enforcement
- **Error boundaries** - Comprehensive error handling
- **Structured logging** - Consistent log formats
- **Type safety** - End-to-end type checking
- **Performance monitoring** - Memory usage tracking
- **Security first** - RLS policies and input validation

## 🔍 Monitoring & Observability

### Built-in Logging
- **Request correlation** - Unique request IDs
- **Performance metrics** - Memory and timing data
- **Error tracking** - Structured error information
- **Business metrics** - Token discovery rates

### Health Checks
- **Database connectivity** - Automatic connection testing
- **API availability** - Service degradation handling
- **Data quality** - Token validation and false positive detection

---

## 🆘 Quick Debug Commands

```bash
# Check environment configuration
env | grep -E "(TG_|TW_|FIRECRAWL|OPENROUTER|SUPABASE)"

# Test database connection
npx tsx scripts/test-supabase-connection.ts

# Review recent logs
tail -f .next/trace

# Check token data quality
# Review database tables in Supabase dashboard

# Validate scraping functions
# Test individual utils in src/utils/
```

This codebase follows modern Next.js patterns with emphasis on reliability, observability, and graceful degradation when external services are unavailable.