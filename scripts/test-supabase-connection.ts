/**
 * Test Supabase Connection
 * 
 * This script tests the connection to your Supabase database and verifies that
 * all required tables exist and are accessible.
 * 
 * Run with: npx tsx scripts/test-supabase-connection.ts
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ Error: .env file not found!');
  console.error('Please create a .env file with your Supabase credentials:');
  console.error('NEXT_PUBLIC_SUPABASE_URL=your_supabase_url');
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key');
  process.exit(1);
}

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY;

// Check if Supabase credentials are set
if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Supabase credentials not found in .env file!');
  console.error('Please set the following environment variables:');
  console.error('NEXT_PUBLIC_SUPABASE_URL=your_supabase_url');
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
  },
  db: {
    schema: 'public',
  },
});

// Tables to check
const tables = [
  'tokens',
  'telegram_messages',
  'twitter_tweets',
  'binance_announcements',
  'scrape_results',
];

// Test connection and check tables
async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('tokens').select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      throw new Error(`Failed to connect to Supabase: ${error.message}`);
    }
    
    console.log('✅ Successfully connected to Supabase!');
    
    // Check each table
    console.log('\n📋 Checking tables:');
    
    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('count(*)', { count: 'exact', head: true });
        
        if (error) {
          console.error(`❌ Table '${table}' error: ${error.message}`);
        } else {
          console.log(`✅ Table '${table}' exists and is accessible`);
        }
      } catch (err) {
        console.error(`❌ Error checking table '${table}': ${err instanceof Error ? err.message : String(err)}`);
      }
    }
    
    // Test inserting and deleting a record (to verify write permissions)
    console.log('\n✏️ Testing write permissions...');
    
    const testId = `test_${Date.now()}`;
    const { error: insertError } = await supabase
      .from('tokens')
      .insert({
        ticker: testId,
        source: 'test',
        timestamp: new Date().toISOString(),
      });
    
    if (insertError) {
      console.error(`❌ Failed to insert test record: ${insertError.message}`);
    } else {
      console.log('✅ Successfully inserted test record');
      
      // Delete the test record
      const { error: deleteError } = await supabase
        .from('tokens')
        .delete()
        .eq('ticker', testId);
      
      if (deleteError) {
        console.error(`❌ Failed to delete test record: ${deleteError.message}`);
      } else {
        console.log('✅ Successfully deleted test record');
      }
    }
    
    console.log('\n🎉 Supabase connection test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the test
testConnection().catch(error => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});
