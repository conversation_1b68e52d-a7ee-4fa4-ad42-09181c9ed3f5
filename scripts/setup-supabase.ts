/**
 * Supabase Setup Script
 *
 * This script sets up the Supabase database for the Binance Token Scraper application.
 * It creates all necessary tables, indexes, and RLS policies.
 *
 * Run with: npx tsx scripts/setup-supabase.ts
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY;

// Check if Supabase credentials are set
if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Supabase credentials not found in .env file!');
  console.error('Please set the following environment variables:');
  console.error('NEXT_PUBLIC_SUPABASE_URL=your_supabase_url');
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
  },
  db: {
    schema: 'public',
  },
});

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Read SQL file
const sqlFilePath = path.join(process.cwd(), 'supabase_setup.sql');
let sqlContent: string;

try {
  sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
} catch (error) {
  console.error('❌ Error reading SQL file:', error instanceof Error ? error.message : String(error));
  console.error('Make sure the supabase_setup.sql file exists in the project root directory.');
  process.exit(1);
}

// Split SQL into individual statements
const sqlStatements = sqlContent
  .split(';')
  .map(statement => statement.trim())
  .filter(statement => statement.length > 0);

// Function to execute SQL statements
async function executeSQL() {
  console.log('🔧 Setting up Supabase database...');
  console.log(`📊 Found ${sqlStatements.length} SQL statements to execute`);

  // Ask for confirmation
  rl.question('⚠️ This will set up the database tables and policies. Continue? (y/n): ', async (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('❌ Setup cancelled');
      rl.close();
      return;
    }

    console.log('\n🚀 Executing SQL statements...');

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      const statementType = getStatementType(statement);

      try {
        const { error } = await supabase.rpc('pgmoon_exec', { query_text: statement });

        if (error) {
          // List of error messages that can be safely ignored
          const ignorableErrors = [
            'role "service_role" already exists',
            'policy "Service role can select',
            'policy "Service role can insert',
            'policy "Service role can update',
            'policy "Service role can delete',
            'policy "Anonymous users can select'
          ];

          // Check if this is an ignorable error
          const isIgnorable = ignorableErrors.some(msg => error.message.includes(msg));

          if (isIgnorable) {
            console.log(`ℹ️ Statement ${i + 1} (${statementType}): Object already exists, skipping`);
            successCount++;
          } else {
            console.error(`❌ Error executing statement ${i + 1} (${statementType}): ${error.message}`);
            errorCount++;
          }
        } else {
          console.log(`✅ Successfully executed statement ${i + 1} (${statementType})`);
          successCount++;
        }
      } catch (error) {
        // Check for errors we can safely ignore
        const errorMessage = error instanceof Error ? error.message : String(error);

        // List of error messages that can be safely ignored
        const ignorableErrors = [
          'role "service_role" already exists',
          'policy "Service role can select',
          'policy "Service role can insert',
          'policy "Service role can update',
          'policy "Service role can delete',
          'policy "Anonymous users can select'
        ];

        // Check if this is an ignorable error
        const isIgnorable = ignorableErrors.some(msg => errorMessage.includes(msg));

        if (isIgnorable) {
          console.log(`ℹ️ Statement ${i + 1} (${statementType}): Object already exists, skipping`);
          successCount++;
        } else {
          console.error(`❌ Error executing statement ${i + 1} (${statementType}):`, errorMessage);
          errorCount++;
        }
      }
    }

    console.log('\n📝 Summary:');
    console.log(`✅ ${successCount} statements executed successfully`);
    console.log(`❌ ${errorCount} statements failed`);

    if (errorCount === 0) {
      console.log('\n🎉 Supabase database setup completed successfully!');
      console.log('🔍 Run the test script to verify the setup: npx tsx scripts/test-supabase-connection.ts');
    } else {
      console.log('\n⚠️ Some statements failed. Please check the errors above and try again.');
      console.log('You may need to execute the SQL statements manually in the Supabase SQL Editor.');
    }

    rl.close();
  });
}

// Function to get the type of SQL statement
function getStatementType(statement: string): string {
  const normalizedStatement = statement.toUpperCase().trim();

  if (normalizedStatement.startsWith('CREATE TABLE')) return 'CREATE TABLE';
  if (normalizedStatement.startsWith('CREATE INDEX')) return 'CREATE INDEX';
  if (normalizedStatement.startsWith('CREATE EXTENSION')) return 'CREATE EXTENSION';
  if (normalizedStatement.startsWith('ALTER TABLE')) return 'ALTER TABLE';
  if (normalizedStatement.startsWith('CREATE POLICY')) return 'CREATE POLICY';
  if (normalizedStatement.startsWith('CREATE ROLE')) return 'CREATE ROLE';
  if (normalizedStatement.startsWith('GRANT')) return 'GRANT';

  return 'OTHER';
}

// Execute the setup
executeSQL().catch(error => {
  console.error('❌ Unhandled error:', error instanceof Error ? error.message : String(error));
  rl.close();
  process.exit(1);
});
